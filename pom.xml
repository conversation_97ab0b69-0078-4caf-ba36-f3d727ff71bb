<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.fxiaoke.cloud</groupId>
    <artifactId>fxiaoke-spring-cloud-parent</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.fxiaoke</groupId>
  <artifactId>fs-file-datax</artifactId>
  <version>1.0-SNAPSHOT</version>
  <modelVersion>4.0.0</modelVersion>

  <packaging>pom</packaging>

  <modules>
    <module>fs-file-datax-backup</module>
    <module>fs-file-datax-transfer</module>
    <module>fs-file-datax-stats</module>
  </modules>

  <properties>
    <fs-restful.version>1.0.2-SNAPSHOT</fs-restful.version>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <java.version>21</java.version>
    <jdk.version>21</jdk.version>
    <aws.java.sdk.version>2.28.28</aws.java.sdk.version>
    <redis.clients.version>3.7.1</redis.clients.version>
    <junit-platform-launcher.version>1.11.2</junit-platform-launcher.version>
  </properties>

  <dependencies>

    <!-- SpringBoot核心依赖 必须 start-->
    <dependency>
      <artifactId>spring-boot-starter-web</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-aop</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <!--SpringBoot核心依赖 必须 end-->

    <!--SpringBoot测试依赖 必须 start-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <version>${junit-platform-launcher.version}</version>
      <scope>test</scope>
    </dependency>
    <!--SpringBoot测试依赖 必须 end-->

   <!--公司组件 必须 start-->
    <!-- 日志自动上报 -->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>metrics-spring-boot-starter</artifactId>
    </dependency>
    <!-- 链路追踪过滤器 -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>core-filter</artifactId>
    </dependency>
    <!--配置中心-->
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>cms-spring-cloud-starter</artifactId>
    </dependency>
    <!--公司组件 必须 end-->

    <!--公司组件-基础组件 按需 start-->
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>${redis.clients.version}</version>
    </dependency>
    <!--公司组件-业务 按需 end-->

    <!--通用工具类-->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>my-prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>

            </goals>
            <configuration>
              <propertyName>jacocoArgLine</propertyName>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--suppress UnresolvedMavenProperty -->
          <argLine>
            ${jacocoArgLine}
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.math=ALL-UNNAMED
          </argLine>
          <systemPropertyVariables>
            <process.profile>fstest</process.profile>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <!--添加打包插件以便支持jar模式运行-->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>