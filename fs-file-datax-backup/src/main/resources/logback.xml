<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <file>${catalina.home}/logs/fs-log.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fs-%d{yyyyMMdd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                java.util,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFile"/>
    </appender>

    <!-- 配置基础组件为WARN级别，避免打印过多影响服务自己日志 -->
    <logger name="druid.sql" level="INFO"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>


    <root level="info">
        <appender-ref ref="ASYNC"/>
    </root>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 配置日志输出格式 -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 配置日志级别 -->
    <root level="info">
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
