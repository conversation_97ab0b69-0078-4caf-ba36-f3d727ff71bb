package com.facishare.datax.backup.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.datax.backup.model.BatchInsertMessage;

import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import jakarta.annotation.PostConstruct;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;


/**
 * creator: liuys
 * CreateTime: 2025-01-09
 * Description:
 */
@Component
public class BatchInsertProvider {
  private AutoConfMQProducer producer;
  private String defaultTopic;

  @PostConstruct
  public void init() {
    producer = new AutoConfMQProducer("fs-file-system-rocketmq-config", "common,file_backup_namesrv,file_backup_insert");
    defaultTopic = producer.getDefaultTopic();
  }

  public void send(BatchInsertMessage message) {
    Message msg = new Message(defaultTopic, JSON.toJSONString(message).getBytes());
    producer.send(msg);
  }
}
