package com.facishare.datax.backup.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.datax.backup.model.BatchInsertMessage;
import com.facishare.datax.backup.model.BigFileBackUpMessage;
import com.facishare.datax.backup.model.EnterpriseCloudInfo;
import com.facishare.datax.backup.service.AliOssService;
import com.facishare.datax.backup.service.AwsS3Service;
import com.facishare.datax.backup.util.S3StorageConfig;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;

import static com.facishare.datax.backup.util.S3StorageConfig.OSS_KEY;

@Component
@Slf4j
@Deprecated
public class BigFileBackUpConsumer{
  @Autowired
  private AliOssService aliOssService;
  @Autowired
  private AwsS3Service awsS3Service;
  @Resource
  S3StorageConfig s3StorageConfig;
  private AutoConfMQPushConsumer consumer;
  @Resource
  private BatchInsertProvider provider;

  @PostConstruct
  public void init() {
    // 大附件暂停消费

//    consumer = new AutoConfMQPushConsumer("fs-file-system-rocketmq-config",
//        "common,consume_backup_namesrv,consumer_stone_big_backUp_new",
//        (MessageListenerConcurrently) (msgs, context) -> {
//          if (!msgs.isEmpty()) {
//            processMessage(msgs);
//          }
//          return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//        });
  }

  public void processMessage(List<MessageExt> messages) {
    for (MessageExt message : messages) {
      BigFileBackUpMessage backUpMessage = JSON.parseObject(new String(message.getBody()), BigFileBackUpMessage.class);
      InputStream inputStream = aliOssService.download(OSS_KEY, backUpMessage.getObjectKey());
      if (inputStream == null) {
        continue;
      }
      //FileUtil.copyFile(backUpMessage.getObjectKey(), inputStream);
      String key = s3StorageConfig.getKey(backUpMessage.getEa());
      awsS3Service.chunkUpload(key, backUpMessage.getObjectKey());
      EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(key);
      String diskNum = enterpriseCloudInfo.getDiskAddr();
      BatchInsertMessage msg = BatchInsertMessage.builder()
          .ea(backUpMessage.getEa())
          .objectKey(backUpMessage.getObjectKey())
          .bucket(enterpriseCloudInfo.getBucket())
          .diskNumber(diskNum)
          .storageType(enterpriseCloudInfo.getCloudType())
          .fileExt(backUpMessage.getExtensionName()).build();
      provider.send(msg);
    }
  }

//
//  @Override
//  public void close() {
//    if (consumer != null) {
//      consumer.shutdown();
//    }
//  }
//
//  @Override
//  public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
//    if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
//      consumer.start();
//    }
//  }
}
