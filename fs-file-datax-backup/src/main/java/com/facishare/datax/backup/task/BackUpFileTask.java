package com.facishare.datax.backup.task;


import com.facishare.datax.backup.consumer.BatchInsertProvider;
import com.facishare.datax.backup.service.AwsS3Service;
import com.facishare.datax.backup.util.S3StorageConfig;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.regex.Pattern;

@Component
@Slf4j
@Deprecated
public class BackUpFileTask {
  @Autowired
  private AwsS3Service awsS3Service;
  @Resource
  S3StorageConfig s3StorageConfig;
  @Resource
  private BatchInsertProvider insertProvider;
  private static Pattern pattern = Pattern.compile("(.+)_(.+)_(.+)\\.[a-zA-Z]+");
  private static final ScheduledExecutorService executor = new ScheduledThreadPoolExecutor(1,
      new ThreadFactoryBuilder().setNameFormat("backup-local-%d").build());
  @Autowired
  private BatchInsertProvider batchInsertProvider;

//  @PostConstruct
//  public void init() {
//    executor.scheduleWithFixedDelay(reUpload(), 1, 20, TimeUnit.SECONDS);
//  }

//  /**
//   * 上传存储在本地持久存储的大文件, 随机选择一个bucket，兜底保证不丢。
//   *
//   * @return
//   */
//  public Runnable reUpload() {
//    return () -> {
//      String localFilePath = FileUtil.getLocalFilePath();
//      log.info("localPath:{}", localFilePath);
//      File[] files = new File(localFilePath).listFiles();
//      if (Objects.isNull(files) || files.length == 0) {
//        log.warn("empty local dir");
//        return;
//      }
//      List<File> fileList = new ArrayList<>(files.length);
//      Collections.addAll(fileList, files);
//      if (fileList.isEmpty()) {
//        log.warn("file list is empty");
//      }
//      fileList.forEach(file -> {
//        if (!pattern.matcher(file.getName()).matches()) {
//          return;
//        }
//        //防止上传冲突,仅上传一段时间之前的
//        if (System.currentTimeMillis() - file.lastModified() < 1000 * 60 * 60 * 24) {
//          return;
//        }
//        log.info("开始上传:{}", file.getName());
//        String obk = CharMatcher.anyOf("_").replaceFrom(file.getName(), "/");
//        awsS3Service.chunkUpload(s3StorageConfig.getRandomKey(), obk);
//        String[] split = obk.split("/");
//        String diskNum = "";
//        EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(s3StorageConfig.getRandomKey());
//        diskNum = enterpriseCloudInfo.getDiskAddr();
//        BatchInsertMessage msg = BatchInsertMessage.builder()
//            .ea(split[0])
//            .objectKey(obk)
//            .bucket(enterpriseCloudInfo.getBucket())
//            .diskNumber(diskNum)
//            .storageType(enterpriseCloudInfo.getCloudType())
//            .fileExt(FileUtil.getFileExt(localFilePath)).build();
//        batchInsertProvider.send(msg);
//      });
//    };
//  }
}
