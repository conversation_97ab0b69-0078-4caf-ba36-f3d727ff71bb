package com.facishare.datax.backup.entity;

import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

public abstract class IdEntity {
  @Id
  protected ObjectId id;

  @Property(value = IdField.createTime)
  protected long createTime = System.currentTimeMillis();

  @Property(value = IdField.updateTime)
  protected long updateTime = System.currentTimeMillis();

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;

    IdEntity idEntity = (IdEntity) o;

    return id != null ? id.equals(idEntity.id) : idEntity.id == null;
  }

  @Override
  public int hashCode() {
    return id != null ? id.hashCode() : 0;
  }

  public ObjectId getId() {
    return id;
  }

  public void setId(ObjectId id) {
    this.id = id;
  }

  public long getCreateTime() {
    return createTime;
  }

  public void setCreateTime(long createTime) {
    this.createTime = createTime;
  }

  public long getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(long updateTime) {
    this.updateTime = updateTime;
  }
}


