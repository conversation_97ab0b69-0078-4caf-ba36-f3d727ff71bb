package com.facishare.datax.backup.util;

import com.facishare.datax.backup.model.EnterpriseCloudInfo;
import com.fxiaoke.common.Guard;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moandjiezana.toml.Toml;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

@Component
@Slf4j
public class S3StorageConfig {
  private Map<String, Toml> enterpriseStorage = Maps.newHashMap();
  private static String key = "1111111111111111";
  public static final String OSS_KEY = "oss";
  private static Guard guard = new Guard(key);
  private static final List<String> randomKeys = Lists.newArrayList();

  @PostConstruct
  public void init() {
    ConfigFactory.getInstance().getConfig("fs-stone-backup-toml", config -> {
      Toml toml = new Toml().read(config.getString());
      for (Map.Entry<String, Object> entry : toml.entrySet()) {
        enterpriseStorage.put(entry.getKey(), (Toml) entry.getValue());
        if (!((Toml) entry.getValue()).getBoolean("isExclusive")) {
          // 存储纷享的备份Key，用于某些需要用我们的备份，但是使用对象存储。这里给他们随机写入
          randomKeys.add(entry.getKey());
        }
      }
    });
  }

  // 3种情况 1. 普通企业，写入我们的备份磁盘。带group 的 2. 普通企业，写入我们的备份磁盘。但是存在对象存储，如元气森林。 3.
  // 文件专属，备份文件写入他们的磁盘。
  public String getKey(String ea) {
    return containsKey(ea) ? ea : getRandomKey();
  }

  public String getRandomKey() {
    return randomKeys.get(ThreadLocalRandom.current().nextInt(randomKeys.size()));
  }

  public boolean containsKey(String ea) {
    return enterpriseStorage.containsKey(ea);
  }

  public EnterpriseCloudInfo getEnterpriseCloudInfo(String key) {
    Toml toml = enterpriseStorage.get(key);
    return EnterpriseCloudInfo.builder()
        .endPoint(toml.getString("endPoint"))
        .bucket(toml.getString("bucket"))
        .apiUrl(toml.getString("apiUrl"))
        .accessKeySecret(decode(toml.getString("accessKeySecret")))
        .accessKeyId(decode(toml.getString("accessKeyId")))
        .diskAddr(toml.getString("diskAddr"))
        .httpProxy(toml.getString("httpProxy"))
        .cloudType(toml.getString("cloudType"))
        .build();
  }



  private String encode(String message) {
    try {
      return guard.encode2(message);
    } catch (Exception e) {
      log.error("encode fail,key:{}", message);
      return null;
    }
  }

  private static String decode(String message) {
    try {
      return guard.decode(message);
    } catch (Exception e) {
      log.error("decode message error,message:{}", message);
      return null;
    }
  }


  public static void main(String[] args) {
    String accessKey = decode("****************************************************************");
    String accessKeySecret = decode(
        "6AA905C78D85C91DC303191D10740DC15553CF6E4A5B7C2825F2609226CB3477BF53B1C4503DD3EEB3B6B272A351B39AE56C2B993A663E92");
    System.out.println(accessKey);
    System.out.println(accessKeySecret);
    System.out.println();
  }

}
