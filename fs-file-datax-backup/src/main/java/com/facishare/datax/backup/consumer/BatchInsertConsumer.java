package com.facishare.datax.backup.consumer;

import com.facishare.datax.backup.entity.BatchInsertEntity;
import com.facishare.datax.backup.model.BatchInsertMessage;
import com.facishare.datax.backup.service.BatchInsertService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultLitePullConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * creator: liuys
 * CreateTime: 2025-01-09
 * Description:
 */

@Slf4j
@Component
public class BatchInsertConsumer {
  private DefaultLitePullConsumer consumer;
  private final AtomicBoolean running = new AtomicBoolean(false);
  private ExecutorService executorService;

  @Resource
  private BatchInsertService batchInsertService;

  private String nameSrv;
  private String topic;
  private String group;
  private long pollSize;
  private long consumeTime;

  @PostConstruct
  public void init() {
    try {
      initConfig();
      initConsumer();
      startConsume();
    } catch (Exception e) {
      log.error("BatchInsertConsumer初始化失败", e);
      throw new RuntimeException("Failed to initialize BatchInsertConsumer", e);
    }
  }

  private void initConfig() {
    ConfigFactory.getConfig("fs-backup-record", config -> {
      nameSrv = config.get("namesrv");
      topic = config.get("topic");
      group = config.get("consumer.group");
      pollSize = config.getLong("max.poll.size");
      consumeTime = config.getLong("max.consume.time");
    });

    if (nameSrv == null || nameSrv.trim().isEmpty()) {
      throw new IllegalArgumentException("RocketMQ nameServer address cannot be null or empty");
    }
  }

  private void initConsumer() throws MQClientException {
    log.info("开始初始化BatchInsertConsumer, group: {}, namesrv: {}, topic: {}", group, nameSrv, topic);
    consumer = new DefaultLitePullConsumer(group);
    consumer.setNamesrvAddr(nameSrv);
    consumer.subscribe(topic);
    consumer.setAutoCommit(false);
    consumer.start();
    running.set(true);
    executorService = Executors.newSingleThreadExecutor();
    log.info("BatchInsertConsumer初始化成功");
  }

  private void startConsume() {
    executorService.submit(this::consumeMessage);
  }

  private void consumeMessage() {
    List<String> msgs = Lists.newArrayList();
    long start = System.currentTimeMillis();

    while (running.get()) {
      try {
        List<MessageExt> messages = consumer.poll(3000);
        if (!messages.isEmpty()) {
          messages.forEach(msg -> msgs.add(new String(msg.getBody())));
        }

        if (System.currentTimeMillis() - start > consumeTime || msgs.size() > pollSize) {
          if (!msgs.isEmpty()) {
            try {
              processMessages(msgs);
              consumer.commit();
              log.info("成功处理并提交了 {} 条消息的偏移量", msgs.size());
            } catch (Exception e) {
              log.error("消息处理或提交偏移量失败，这批消息将在下次重试", e);
            }
          }
          start = System.currentTimeMillis();
          msgs.clear();
        }
      } catch (Exception e) {
        log.error("消息消费过程中发生错误", e);
      }
    }
  }

  private void processMessages(List<String> msgs) {
    List<BatchInsertMessage> list = msgs.stream()
        .map(BatchInsertMessage::parseFromString)
        .toList();

    boolean execute = false;
    int retryCount = 0;

    while (!execute && running.get()) {
      try {
        batchInsertService.batchInsert(
            list.stream()
                .map(BatchInsertEntity::convertFromMessage)
                .toList());
        execute = true;
        log.info("批量插入成功处理 {} 条消息", list.size());
      } catch (Exception e) {
        retryCount++;
        log.error("批量插入失败，这是第{}次重试，将继续重试直到成功", retryCount, e);
      }
    }
  }

  @PreDestroy
  public void shutdown() {
    log.info("开始优雅关闭BatchInsertConsumer...");
    // 设置运行状态为false，停止消费循环
    running.set(false);

    try {
      // 等待当前消费线程完成
      if (executorService != null) {
        executorService.shutdown();
      }

      // 关闭消费者
      if (consumer != null) {
        try {
          consumer.commit();
          log.info("成功提交最后的消费位点");
        } catch (Exception e) {
          log.error("提交最后的消费位点失败", e);
        }
        consumer.shutdown();
      }

      log.info("BatchInsertConsumer已完成优雅关闭");
    } catch (Exception e) {
      log.error("BatchInsertConsumer关闭过程中发生错误", e);
      if (executorService != null) {
        executorService.shutdownNow();
      }
      if (consumer != null) {
        consumer.shutdown();
      }
    }
  }
}