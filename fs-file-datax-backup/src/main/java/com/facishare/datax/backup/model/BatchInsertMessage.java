package com.facishare.datax.backup.model;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * creator: liuys
 * CreateTime: 2025-01-09
 * Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchInsertMessage {
  private String ea;
  private String path;
  private String trackerCluster;
  private String storageGroup;
  private String masterId;
  private String clusterMinio;
  private String bucket;
  private String objectKey;
  private String diskNumber;
  private long fileSize;
  private String fileExt;
  private Date fileCreateTime;
  private Date backupTime;
  private String storageType;

  public static BatchInsertMessage parseFromString(String msg) {
    return JSON.parseObject(msg, BatchInsertMessage.class);
  }
}
