package com.facishare.datax.backup.service;


import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.client.StoneDataApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.InputStream;


@Component
@Slf4j
public class FastdfsService {

    @Resource
    private StoneDataApi stoneDataApi;

    public InputStream download(String trackerGroup ,String group, String masterId) {
        try {
            log.info("download file from fastdfs, trackerGroup:{}, group:{}, masterId:{}", trackerGroup, group, masterId);
            return stoneDataApi.downloadByStream(trackerGroup, group, masterId);
        } catch (FRestClientException e) {
            log.warn("download file from fastdfs failed, trackerGroup:{}, group:{}, masterId:{}", trackerGroup, group, masterId, e);
            return null;
        }
    }
}
