package com.facishare.datax.backup.model;

import com.facishare.warehouse.api.util.JsonUtils;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class FileOperationEvent {

    /**
     * 企业账
     */
    private String enterpriseAccount;
    /**
     * 操作者
     */
    private String sourceUser;
    /**
     * 文件大小
     */
    private long size;
    /**
     * 操作类型
     */
    private Operation operation;
    /**
     * 业务类型
     */
    private String business;
    /**
     * 文件扩展名
     */
    private String fileExtension;
    /**
     * 文件nPath
     */
    private String fileName;

    /**
     * 是否是图片
     */
    private boolean image;
    private String ip;
    private String caller;


    private Date operateDate;
    private boolean chunkFile;
    private String masterFile;

    private String trackerGroup;
    private String master;
    private String group;
    private boolean bigStorage;
    private boolean gc;
    private boolean thumbnail;
    private boolean tempFile;
    private long statisticsTimestamp;
    private long expiredTimeStamp;
    private String code;

    private boolean cloudStorage;
    private String objectKey;
    private String bucket;
    private String cloudType;


    /**
     * 兼容历史接口,不再往ES存储
     */
    private String year;
    private String month;
    private String day;
    private String hour;
    private String minute;
    private int quarter;
    private int weekday;
    private String processName;
    private String fastdfsGroup;

    public static FileOperationEvent fromJson(String json) {
        return JsonUtils.fromJson(json, FileOperationEvent.class);
    }

    public String toJson() {
        return JsonUtils.toJson(this);
    }

    public enum Operation {
        UPLOAD, DOWNLOAD, DELETE;
    }
}
