package com.facishare.datax.backup.service;

import com.alibaba.fastjson.JSON;
import com.facishare.datax.backup.entity.BatchInsertEntity;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.jdbc.JdbcConnection;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * creator: liuys
 * CreateTime: 2025-01-09
 * Description: 批量写入CH  
 */
@Slf4j
@Service
public class BatchInsertService {

  private String dataUrl;
  private String username;
  private String password;
  private JdbcConnection jdbcConnection;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-backup-record", config -> {
      dataUrl = config.get("masterUrl");
      username = config.get("username");
      password = config.get("password");
      if (StringUtils.isNotBlank(password)) {
        password = PasswordUtil.decode(password);
      }
      jdbcConnection= new JdbcConnection(dataUrl, username, password);
    });
  }


  public void batchInsert(List<BatchInsertEntity> batchInsertList) throws SQLException {
    String sql = "INSERT INTO back_up (ea, path, tracker_cluster, storage_group, master_id, cluster_minio, bucket, object_key, disk_number, file_size, file_ext, file_create_time, backup_time, storage_type) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    Connection connection = jdbcConnection.connection();
    PreparedStatement preparedStatement = connection.prepareStatement(sql);
    // 使用批处理插入数据
    handlerSql(preparedStatement, batchInsertList);

    // 执行批处理
    preparedStatement.executeBatch();
  }

  private void handlerSql(PreparedStatement preparedStatement, List<BatchInsertEntity> batchInsertList)
      throws SQLException {
    for (BatchInsertEntity batchInsert : batchInsertList) {
      log.info("insert info: {}", JSON.toJSONString(batchInsert));
      preparedStatement.setString(1, batchInsert.getEa() != null ? batchInsert.getEa() : "");
      preparedStatement.setString(2, batchInsert.getPath() != null ? batchInsert.getPath() : "");
      preparedStatement.setString(3, batchInsert.getTrackerCluster() != null ? batchInsert.getTrackerCluster() : "");
      preparedStatement.setString(4, batchInsert.getStorageGroup() != null ? batchInsert.getStorageGroup() : "");
      preparedStatement.setString(5, batchInsert.getMasterId() != null ? batchInsert.getMasterId() : "");
      preparedStatement.setString(6, batchInsert.getClusterMinio() != null ? batchInsert.getClusterMinio() : "");
      preparedStatement.setString(7, batchInsert.getBucket() != null ? batchInsert.getBucket() : "");
      preparedStatement.setString(8, batchInsert.getObjectKey() != null ? batchInsert.getObjectKey() : "");
      preparedStatement.setString(9, batchInsert.getDiskNumber() != null ? batchInsert.getDiskNumber() : "");
      preparedStatement.setLong(10, batchInsert.getFileSize());
      preparedStatement.setString(11, batchInsert.getFileExt() != null ? batchInsert.getFileExt() : "");
      preparedStatement.setTimestamp(12,
          batchInsert.getFileCreateTime() != null ? new java.sql.Timestamp(batchInsert.getFileCreateTime().getTime())
              : new java.sql.Timestamp(System.currentTimeMillis()));
      preparedStatement.setTimestamp(13,
          batchInsert.getBackupTime() != null ? new java.sql.Timestamp(batchInsert.getBackupTime().getTime())
              : new java.sql.Timestamp(System.currentTimeMillis()));
      preparedStatement.setString(14, batchInsert.getStorageType() != null ? batchInsert.getStorageType() : "");

      // 将当前插入操作添加到批处理中
      preparedStatement.addBatch();
    }
  }

}
