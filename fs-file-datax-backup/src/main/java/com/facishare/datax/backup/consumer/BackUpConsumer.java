package com.facishare.datax.backup.consumer;

import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Component
public class BackUpConsumer {
  private AutoConfMQPushConsumer backupConsumer;
  private boolean backupCloseSwitch;
  private boolean isStarted = false;
  @Resource
  BackUpListener backUpListener;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-file-system-config", config -> {
      backupConsumer = new AutoConfMQPushConsumer("fs-file-system-rocketmq-config",
          "common,consume_backup_namesrv,consumer_stone_backup_new", backUpListener);
      backupCloseSwitch = config.getBool("backUpOpenSwitch", true);
      if (backupCloseSwitch) {
        startMQ();
      } else {
        pauseMQ();
      }
    });
  }

  private void startMQ() {
    if (!isStarted) {
      backupConsumer.start();
      isStarted = true;
      log.info("MQ消费者已启动");
    } else {
      backupConsumer.resume();
      log.info("MQ消费者已恢复");
    }
  }

  private void pauseMQ() {
    if (isStarted) {
      backupConsumer.suspend();
      log.info("MQ消费者已暂停");
    } else {
      log.info("MQ消费者未启动，无需暂停");
    }
  }
}
