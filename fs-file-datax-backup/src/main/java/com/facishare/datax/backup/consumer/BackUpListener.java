package com.facishare.datax.backup.consumer;

import com.facishare.datax.backup.model.BatchInsertMessage;
import com.facishare.datax.backup.model.EnterpriseCloudInfo;
import com.facishare.datax.backup.model.FileOperationEvent;
import com.facishare.datax.backup.service.AwsS3Service;
import com.facishare.datax.backup.service.FastdfsService;
import com.facishare.datax.backup.util.S3StorageConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * creator: liuys
 * CreateTime: 2025-01-07
 * Description:
 */
@Component
@Slf4j
public class BackUpListener implements MessageListenerOrderly {
  @Autowired
  private FastdfsService fastdfsService;
  @Autowired
  private S3StorageConfig s3StorageConfig;
  @Autowired
  private AwsS3Service awsS3Service;
  @Resource
  private BatchInsertProvider insertProvider;

  @Override
  public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
    if (!msgs.isEmpty()) {
      try {
        processMessage(msgs);
      } catch (SQLException e) {
        throw new RuntimeException(e);
      }
    }
    return ConsumeOrderlyStatus.SUCCESS;
  }

  public void processMessage(List<MessageExt> messages) throws SQLException {
    log.info("consumer size : {}", messages.size());
    for (MessageExt messageExt : messages) {
      FileOperationEvent fileOperationEvent = FileOperationEvent.fromJson(new String(messageExt.getBody()));
      if (fileOperationEvent.isTempFile() || fileOperationEvent.isCloudStorage() || fileOperationEvent.getOperation() != FileOperationEvent.Operation.UPLOAD) {
        continue;
      }
      InputStream inputStream = fastdfsService.download(fileOperationEvent.getFastdfsGroup(), fileOperationEvent.getGroup(), fileOperationEvent.getMaster());
      if (inputStream == null) {
        log.warn("ea:{},file:{},下载失败 跳过!,FileOperationEvent={}", fileOperationEvent.getEnterpriseAccount(), fileOperationEvent.getFileName(), fileOperationEvent.toJson());
        continue;
      }
      String ea = fileOperationEvent.getEnterpriseAccount();
      // 拼上 group 这种作为标识
      String objectName = fileOperationEvent.getGroup() + "/" + fileOperationEvent.getMaster();
      // 3种情况 1. 普通企业，写入我们的备份磁盘。带group的， group 作为Key 2. 普通企业，写入我们的备份磁盘。但是存在对象存储，如元气森林, 随机选择一套配置Key 。 3. 文件专属，备份文件写入他们的磁盘， ea 作为Key。
      String key = s3StorageConfig.getKey(ea);
      // case 1  带group的普通企业，使用group作为配置Key
      if ((!s3StorageConfig.containsKey(ea)) && StringUtils.isNotBlank(fileOperationEvent.getGroup())) {
        key = fileOperationEvent.getFastdfsGroup();
      }
      EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(key);
      String diskAddr = enterpriseCloudInfo.getDiskAddr();
      boolean upload = awsS3Service.upload(key, objectName, inputStream, fileOperationEvent.getSize());
      if (upload) {
        BatchInsertMessage message = BatchInsertMessage.builder()
            .ea(ea)
            .path(fileOperationEvent.getFileName())
            .diskNumber(diskAddr)
            .masterId(fileOperationEvent.getMaster())
            .storageGroup(fileOperationEvent.getGroup())
            .objectKey(objectName)
            .bucket(enterpriseCloudInfo.getBucket())
            .clusterMinio(fileOperationEvent.getFastdfsGroup())
            .trackerCluster(fileOperationEvent.getFastdfsGroup())
            .fileSize(fileOperationEvent.getSize())
            .fileExt(fileOperationEvent.getFileExtension())
            .fileCreateTime(new Date(fileOperationEvent.getStatisticsTimestamp()))
            .backupTime(new Date(System.currentTimeMillis()))
            .storageType(enterpriseCloudInfo.getCloudType())
            .build();
        insertProvider.send(message);
      }
    }
  }
}
