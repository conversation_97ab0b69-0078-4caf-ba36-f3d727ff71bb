package com.facishare.datax.backup.entity;

import com.alibaba.fastjson.JSON;
import com.facishare.datax.backup.model.BatchInsertMessage;
import lombok.*;

import java.util.Date;

/**
 * creator: liuys
 * CreateTime: 2025-01-09
 * Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BatchInsertEntity {
  private String ea;
  private String path;
  private String trackerCluster;
  private String storageGroup;
  private String masterId;
  private String clusterMinio;
  private String bucket;
  private String objectKey;
  private String diskNumber;
  private long fileSize;
  private String fileExt;
  private Date fileCreateTime;
  private Date backupTime;
  private String storageType;

  public static BatchInsertEntity parseFromString(String msg) {
    return JSON.parseObject(msg, BatchInsertEntity.class);
  }

  public static BatchInsertEntity convertFromMessage(BatchInsertMessage message) {
    return BatchInsertEntity.builder()
        .ea(message.getEa())
        .path(message.getPath())
        .trackerCluster(message.getTrackerCluster())
        .storageGroup(message.getStorageGroup())
        .masterId(message.getMasterId())
        .clusterMinio(message.getClusterMinio())
        .bucket(message.getBucket())
        .objectKey(message.getObjectKey())
        .diskNumber(message.getDiskNumber())
        .fileSize(message.getFileSize())
        .fileExt(message.getFileExt())
        .fileCreateTime(message.getFileCreateTime())
        .backupTime(message.getBackupTime())
        .storageType(message.getStorageType())
        .build();
  }
}
