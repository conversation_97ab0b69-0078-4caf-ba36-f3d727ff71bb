package com.facishare.datax.backup.service;

import com.facishare.datax.backup.entity.FileMetaEntity;
import com.facishare.datax.backup.entity.FileMetaField;
import com.github.mongo.support.DatastoreExt;
import lombok.extern.slf4j.Slf4j;
import org.mongodb.morphia.query.FindOptions;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class MongoService {
    @Autowired
    private DatastoreExt mongoClient;

    /**
     * 查找没被gc的文件
     * @return
     */
    public List<FileMetaEntity> findActiveData(int limit, int skip){
        Query<FileMetaEntity> query = mongoClient.createQuery(FileMetaEntity.class);
        query.criteria(FileMetaField.ossDeleted).equal(false).criteria(FileMetaField.deleted).equal(false).criteria("IT").doesNotExist();
        FindOptions findOptions = new FindOptions();
        findOptions.limit(limit);
        findOptions.skip(skip);
        List<FileMetaEntity> fileMetaEntities = query.asList(findOptions);
        return fileMetaEntities;
    }

    public void change(String objectKey){
        log.info("change meta:{}",objectKey);
        Query<FileMetaEntity> query = mongoClient.createQuery(FileMetaEntity.class);
        query.criteria(FileMetaField.objectKey).equal(objectKey);
        UpdateOperations<FileMetaEntity> updateOperations = mongoClient.createUpdateOperations(FileMetaEntity.class);
        updateOperations.set("IT",true);
        mongoClient.findAndModify(query,updateOperations);
        log.info("change meta:{} success!",objectKey);
    }




}
