package com.facishare.datax.backup.config;

import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.stone.client.StoneDataApi;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.metrics.MetricsConfiguration;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import com.github.mybatis.spring.StaticDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

import javax.sql.DataSource;

/**
 * creator: liuys
 * CreateTime: 2024-11-07
 * Description: 统一的Bean配置类，将所有XML配置迁移到Java配置
 */
@Configuration
@ImportResource(locations = "classpath*:spring/ei-ea-converter.xml")
public class BeanConfig {
    @Bean
    public DataSource dataSource() {
        StaticDataSource staticDataSource = new StaticDataSource();
        staticDataSource.setConfigName("fs-backup-record");
        return staticDataSource;
    }

    /**
     * Metrics配置
     */
    @Bean
    public MetricsConfiguration metricsConfiguration() {
        return new MetricsConfiguration();
    }

    /**
     * HTTP客户端支持配置
     */
    @Bean
    public HttpSupportFactoryBean httpClientSupport() {
        HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
        factoryBean.setConfigName("fs-stone-cloud-http");
        return factoryBean;
    }

    /**
     * MongoDB数据存储配置
     */
    @Bean
    public MongoDataStoreFactoryBean datastore() {
        MongoDataStoreFactoryBean factoryBean = new MongoDataStoreFactoryBean();
        factoryBean.setConfigName("fs-bfm-mongo");
        return factoryBean;
    }

    /**
     * Stone数据API代理配置
     */
    @Bean
    public FRestApiProxyFactoryBean stoneDataApi() {
        FRestApiProxyFactoryBean factoryBean = new FRestApiProxyFactoryBean();
        factoryBean.setType(StoneDataApi.class);
        return factoryBean;
    }
}
