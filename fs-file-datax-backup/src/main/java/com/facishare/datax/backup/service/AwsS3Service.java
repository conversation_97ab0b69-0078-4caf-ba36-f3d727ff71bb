package com.facishare.datax.backup.service;


import com.facishare.datax.backup.model.EnterpriseCloudInfo;
import com.facishare.datax.backup.util.FileUtil;
import com.facishare.datax.backup.util.S3StorageConfig;
import com.google.common.base.CharMatcher;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * creator: liuys
 * CreateTime: 2024-10-24
 * Description: Aws SDK s3上传支持类
 */
@Component
@Slf4j
public class AwsS3Service {

  @Resource
  private S3StorageConfig s3StorageConfig;
  LoadingCache<String, S3Client> awsClientCache;
  private static final int PART_SIZE = 10 * 1024 * 1024;

  @PostConstruct
  public void init() {
    awsClientCache = CacheBuilder.newBuilder().build(
        new CacheLoader<>() {
          @NotNull
          @Override
          public S3Client load(@NotNull String key) {
            return initAwsS3Client(key);
          }
        });
  }

  private S3Client initAwsS3Client(String key) {
    EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(key);
    AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(enterpriseCloudInfo.getAccessKeyId(),
        enterpriseCloudInfo.getAccessKeySecret());
    return S3Client.builder()
        .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
        .endpointOverride(URI.create(enterpriseCloudInfo.getEndPoint()))
        .region(Region.CN_NORTH_1)
        .build();
  }

  public S3Client getAwsS3Client(String key) {
    try {
      return awsClientCache.get(key);
    } catch (ExecutionException e) {
      log.error("error init s3 Client");
      throw new RuntimeException(e);
    }
  }

  public boolean upload(String key, String objectKey, InputStream inputStream, long  size) {
    try {
      S3Client s3Client = getAwsS3Client(key);
      EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(key);

      PutObjectRequest request = PutObjectRequest.builder()
          .bucket(enterpriseCloudInfo.getBucket())
          .key(objectKey)
          .build();
      PutObjectResponse putObjectResponse = s3Client.putObject(request,
          RequestBody.fromInputStream(inputStream, size));
      log.info("upload info :{}", putObjectResponse);
      return true;
    } catch (Exception e) {
      log.error("文件FastDfsMinio上传失败", e);
      return false;
    }
  }

    public boolean chunkUpload(String key, String objectKey) {
        EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(key);
        S3Client s3Client = getAwsS3Client(key);
        String uploadId = initMultipartUpload(enterpriseCloudInfo, s3Client, objectKey);
        List<CompletedPart> partETags = Lists.newArrayList();
        String localFile = FileUtil.getLocalFilePath() + CharMatcher.anyOf("/").replaceFrom(objectKey,"_"); // 操作系统中会将“/” 转换成 “_”
        Path path = Path.of(localFile);
        try (InputStream inputStream = Files.newInputStream(path)) {
            byte[] buffer = new byte[PART_SIZE];
            int bytesRead;
            int partNumber = 1;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                UploadPartRequest upload = UploadPartRequest.builder()
                    .bucket(enterpriseCloudInfo.getBucket())
                    .key(objectKey)
                    .uploadId(uploadId)
                    .partNumber(partNumber)
                    .contentLength((long) bytesRead)
                    .build();
                UploadPartResponse uploadPartResponse = s3Client.uploadPart(upload, RequestBody.fromBytes(buffer));
                partETags.add(CompletedPart.builder()
                    .partNumber(partNumber)
                    .eTag(uploadPartResponse.eTag())
                    .build());
                partNumber++;
            }
            // 完成分片
            CompleteMultipartUploadResponse completeMultipartUploadResponse = finishMultipartUpload(enterpriseCloudInfo, s3Client, objectKey, uploadId, partETags);
            FileUtil.deleteFile(localFile);
            return !Objects.isNull(completeMultipartUploadResponse);
        } catch (IOException e) {
            log.error("chunk upload error");
            return false;
        }
    }

  public InputStream download(String configName, String objectKey) {
    EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(configName);
    S3Client awsS3Client = getAwsS3Client(configName);
    GetObjectRequest getObjectRequest = GetObjectRequest.builder()
        .bucket(enterpriseCloudInfo.getBucket())
        .key(objectKey)
        .build();

    return awsS3Client.getObject(getObjectRequest);
  }

  private String initMultipartUpload(EnterpriseCloudInfo enterpriseCloudInfo, S3Client s3Client, String objectKey) {
    CreateMultipartUploadRequest request = CreateMultipartUploadRequest.builder()
        .bucket(enterpriseCloudInfo.getBucket())
        .key(objectKey)
        .build();
    CreateMultipartUploadResponse multipartUpload = s3Client.createMultipartUpload(request);
    return multipartUpload.uploadId();
  }

  private CompleteMultipartUploadResponse finishMultipartUpload(EnterpriseCloudInfo enterpriseCloudInfo,
      S3Client s3Client, String objectKey, String uploadId, List<CompletedPart> partETags) {
    CompleteMultipartUploadRequest completeMultipartUploadRequest = CompleteMultipartUploadRequest.builder()
        .bucket(enterpriseCloudInfo.getBucket())
        .key(objectKey)
        .uploadId(uploadId)
        .multipartUpload(multipartUpload -> multipartUpload.parts(partETags)).build();
    return s3Client.completeMultipartUpload(completeMultipartUploadRequest);

  }
}
