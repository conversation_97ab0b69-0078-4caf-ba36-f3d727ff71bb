package com.facishare.datax.backup.util;

import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;


@UtilityClass
@Slf4j
public class FileUtil {

    private String localFilePath;
    static {
        ConfigFactory.getInstance().getConfig("fs-stone-backup-config",config -> {
            localFilePath = config.get("local.file.path","/opt/fs-bfm/");
        });
        File dir = new File(localFilePath);
        if(!dir.exists()){
            dir.mkdir();
        }

    }

    public String getLocalFilePath(){
        return localFilePath;
    }

    public String copyFile(String objectKey, InputStream inputStream){
        try {
            String path = localFilePath + CharMatcher.anyOf("/").replaceFrom(objectKey, "_");
            FileOutputStream fileOutputStream = new FileOutputStream(path);
            IOUtils.copy(inputStream,fileOutputStream);
            return path;
        } catch (IOException e) {
            log.error("转储本地失败,obk:{}",objectKey,e);
        }
        return null;
    }

    /**
     * 删除暂存本地的文件
     * @param objectKey
     */
    public void deleteFile(String objectKey){
        new File(localFilePath + CharMatcher.anyOf("/").replaceFrom(objectKey,"_")).delete();
        log.info("delete File success,objectKey:{}",objectKey);
    }
}
