package com.facishare.datax.backup.service;


import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.facishare.datax.backup.model.EnterpriseCloudInfo;
import com.facishare.datax.backup.util.S3StorageConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;

@Component
@Slf4j
public class AliOssService {
  private OSS ossClient;
  @Autowired
  S3StorageConfig s3StorageConfig;

  public OSS getOssClient(String ea) {
    EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(ea);
    ClientConfiguration clientConfiguration = new ClientConfiguration();
    clientConfiguration.setConnectionTimeout(10000);
    clientConfiguration.setSocketTimeout(60000);
    String httpProxy = enterpriseCloudInfo.getHttpProxy();
    if (StringUtils.isNotBlank(httpProxy)) {
      clientConfiguration.setProxyPort(Integer.parseInt(httpProxy.substring(httpProxy.indexOf(":") + 1)));
      clientConfiguration.setProxyHost(httpProxy.substring(0, httpProxy.indexOf(":")));
    }
    ossClient = new OSSClient(enterpriseCloudInfo.getEndPoint(), enterpriseCloudInfo.getAccessKeyId(), enterpriseCloudInfo.getAccessKeySecret(), clientConfiguration);
    return ossClient;
  }

  public InputStream download(String key, String objectKey) {
    try {
      EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(key);
      OSSObject object = getOssClient(key).getObject(enterpriseCloudInfo.getBucket(), objectKey);
      if (object.getResponse().isSuccessful()) {
        log.info("download file success,config:{},objectKey:{}", key, objectKey);
        return object.getObjectContent();
      } else {
        log.error("download file fail,obk:{},cause:{}", objectKey, object.getResponse().getErrorResponseAsString());
        return null;
      }
    } catch (Throwable throwable) {
      log.error("download file fail,object:{}", objectKey);
      return null;
    }
  }

  /**
   * 列举对象
   *
   * @param config
   * @return
   */
  public List<OSSObjectSummary> listObjects(String config) {
    EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(config);
    ObjectListing objectListing = getOssClient(config).listObjects(enterpriseCloudInfo.getBucket());
    return objectListing.getObjectSummaries();
  }

  public void delete(String config, String objectKey) {
    EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(config);
    getOssClient(config).deleteObject(enterpriseCloudInfo.getBucket(), objectKey);
  }

  public boolean exists(String objectKey, String config) {
    EnterpriseCloudInfo enterpriseCloudInfo = s3StorageConfig.getEnterpriseCloudInfo(config);
    return getOssClient(config).doesObjectExist(enterpriseCloudInfo.getBucket(), objectKey);
  }


}
