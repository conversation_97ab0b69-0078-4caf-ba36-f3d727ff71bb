package com.facishare.datax.backup.model;


import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Setter
@Getter
public class Employee implements Serializable {
  private Integer employeeId;  //员工id
  private String enterpriseAccount; // 所属企业账号

  public Employee() {
  }

  public Employee(String enterpriseAccount, int employeeId) {
    this.enterpriseAccount = enterpriseAccount;
    this.employeeId = employeeId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    Employee employee = (Employee) o;
    return Objects.equals(employeeId, employee.employeeId) &&
      Objects.equals(enterpriseAccount, employee.enterpriseAccount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(enterpriseAccount, employeeId);
  }

  @Override
  public String toString() {
    return "Employee{" +
      "enterpriseAccount='" + enterpriseAccount + '\'' +
      ", employeeId=" + employeeId +
      '}';
  }
}
