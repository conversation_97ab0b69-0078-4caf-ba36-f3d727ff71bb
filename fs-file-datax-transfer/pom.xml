<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-file-datax</artifactId>
    <version>1.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>fs-file-datax-transfer</artifactId>

  <dependencies>
    <!--公司组件-基础组件 按需 start-->

    <!--MongoDB-->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>mongo-spring-boot-starter</artifactId>
    </dependency>
    <!--Redis-->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>

    <!--获取企业基本信息-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-active-session-manage-api</artifactId>
    </dependency>
    <!--企业EA/EI转换-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
    </dependency>

    <!--公司组件-业务 按需 end-->
  </dependencies>
</project>