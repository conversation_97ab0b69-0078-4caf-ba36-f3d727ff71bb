package com.fxiaoke.file.datax.transfer.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@Slf4j(topic = "CmsPropertiesConfig")
@ConfigurationProperties(prefix = "cms.file.datax.transfer")
public class CmsPropertiesConfig {
  // 按时间分片查询数据的时间间隔，单位分钟，默认30分钟
  private int dataQueryPartSizeInMinutes=30;
}
