package com.fxiaoke.com.file.datax.stats.domain.entity.model;


import com.fxiaoke.com.file.datax.stats.domain.fieids.NS2Mp3SubFileFieIds;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class NS2Mp3SubFile {

  @Property(NS2Mp3SubFileFieIds.group)
  public String group;
  @Property(NS2Mp3SubFileFieIds.masterId)
  public String masterId;
  @Property(NS2Mp3SubFileFieIds.storageIp)
  private String storageIp;
  @Property(NS2Mp3SubFileFieIds.date)
  private Date date;
}
