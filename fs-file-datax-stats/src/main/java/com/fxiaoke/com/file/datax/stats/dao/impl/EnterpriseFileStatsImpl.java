package com.fxiaoke.com.file.datax.stats.dao.impl;

import com.fxiaoke.com.file.datax.stats.dao.EnterpriseFileStatsDao;
import com.fxiaoke.com.file.datax.stats.domain.constants.ErInfo;
import com.fxiaoke.com.file.datax.stats.domain.entity.EnterpriseFileStats;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.fxiaoke.com.file.datax.stats.domain.fieids.EnterpriseFileStatsFieIds;
import com.fxiaoke.com.file.datax.stats.domain.model.IncFileUsedQuotaItem;
import com.github.mongo.support.DatastoreExt;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mongodb.BasicDBObject;
import com.mongodb.BulkWriteOperation;
import com.mongodb.BulkWriteResult;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j(topic = "EnterpriseFileStatsDaoImpl")
public class EnterpriseFileStatsImpl implements EnterpriseFileStatsDao {

  private static final String MODULE = "EnterpriseFileStatsImpl";

  private final DatastoreExt dataStore;

  // 服务启动时调用索引创建
  private final Cache<String, Boolean> statsExistCache = CacheBuilder.newBuilder()
      .maximumSize(100000) // 设置缓存大小
      .build();

  public EnterpriseFileStatsImpl(@Qualifier("warehouseDataStore") DatastoreExt warehouseDataStore) {
    this.dataStore = warehouseDataStore;
    initIndex();
  }

  private void initIndex() {
    dataStore.ensureIndexes(EnterpriseFileStats.class);
  }

  @Override
  public void incUsedQuota(IncFileUsedQuotaItem item) {
    try {
      Boolean exist = statsExistCache.getIfPresent(item.getEa());
      if (exist == null) {
        upsertIncUsedQuota(item);
        statsExistCache.put(item.getEa(), Boolean.TRUE);
      } else {
        IncUsedQuota(item);
      }
    } catch (Exception e) {
      throw new BaseException(e,MODULE+".incUsedQuota", ErInfo.DATABASE_INC_QUOTA_FAIL,item);
    }
  }

  @Override
  public void incUsedQuotas(List<IncFileUsedQuotaItem> items) {

    if (items == null || items.isEmpty()) {
      return;
    }

    try {
      // 1. 数据预处理：按EA分组合并
      Map<String, IncFileUsedQuotaItem> groupedItems = mergeItemsByEA(items);
      
      // 2. 缓存检查：分离upsert和update操作
      List<IncFileUsedQuotaItem> upsertItems = new ArrayList<>();
      List<IncFileUsedQuotaItem> updateItems = new ArrayList<>();
      
      for (IncFileUsedQuotaItem item : groupedItems.values()) {
        Boolean exist = statsExistCache.getIfPresent(item.getEa());
        if (exist == null) {
          upsertItems.add(item);
        } else {
          updateItems.add(item);
        }
      }
      
      // 3. 批量upsert：处理新记录
      if (!upsertItems.isEmpty()) {
        batchUpsertIncUsedQuota(upsertItems);
        // 更新缓存
        for (IncFileUsedQuotaItem item : upsertItems) {
          statsExistCache.put(item.getEa(), Boolean.TRUE);
        }
      }
      
      // 4. 批量update：处理已存在记录
      if (!updateItems.isEmpty()) {
        batchIncUsedQuota(updateItems);
      }
      
    } catch (Exception e) {
      throw new BaseException(e,MODULE+".incUsedQuota", ErInfo.DATABASE_INC_QUOTA_FAIL,items);
    }
  }
  
  /**
   * 数据预处理：按EA分组合并
   */
  private Map<String, IncFileUsedQuotaItem> mergeItemsByEA(List<IncFileUsedQuotaItem> items) {
    return items.stream().collect(Collectors.toMap(
        IncFileUsedQuotaItem::getEa,
        item -> item,
        (existing, replacement) -> new IncFileUsedQuotaItem(
            existing.getEa(),
            existing.getQuota() + replacement.getQuota(),
            existing.getCount() + replacement.getCount()
        )
    ));
  }
  
  /**
   * 批量upsert操作：处理新记录
   */
  private void batchUpsertIncUsedQuota(List<IncFileUsedQuotaItem> items) {
    DBCollection collection = dataStore.getCollection(EnterpriseFileStats.class);
    BulkWriteOperation bulkWrite = collection.initializeUnorderedBulkOperation();
    
    for (IncFileUsedQuotaItem item : items) {
      // 查询条件
      DBObject query = new BasicDBObject(EnterpriseFileStatsFieIds.ea, item.getEa());
      
      // 更新操作
      DBObject update = new BasicDBObject();
      DBObject setOnInsert = new BasicDBObject()
          .append(EnterpriseFileStatsFieIds.ea, item.getEa())
          .append(EnterpriseFileStatsFieIds.licenseQuota, 0L)
          .append(EnterpriseFileStatsFieIds.createTime, System.currentTimeMillis())
          .append(EnterpriseFileStatsFieIds.version, 1)
          .append(EnterpriseFileStatsFieIds.preset, 0);
      
      DBObject inc = new BasicDBObject()
          .append(EnterpriseFileStatsFieIds.usedQuota, item.getQuota())
          .append(EnterpriseFileStatsFieIds.fileCount, item.getCount());
      
      DBObject set = new BasicDBObject()
          .append(EnterpriseFileStatsFieIds.lastUpdateTime, System.currentTimeMillis());
      
      update.put("$setOnInsert", setOnInsert);
      update.put("$inc", inc);
      update.put("$set", set);
      
      bulkWrite.find(query).upsert().updateOne(update);
    }

    BulkWriteResult result = bulkWrite.execute();
    log.debug("Batch upsert completed, matched: {}, upserted: {}, modified: {}",
        result.getMatchedCount(), result.getUpserts().size(), result.getModifiedCount());
  }
  
  /**
   * 批量update操作：处理已存在记录
   */
  private void batchIncUsedQuota(List<IncFileUsedQuotaItem> items) {
    DBCollection collection = dataStore.getCollection(EnterpriseFileStats.class);
    BulkWriteOperation bulkWrite = collection.initializeUnorderedBulkOperation();
    
    for (IncFileUsedQuotaItem item : items) {
      // 查询条件
      DBObject query = new BasicDBObject(EnterpriseFileStatsFieIds.ea, item.getEa());
      
      // 更新操作
      DBObject update = new BasicDBObject();
      DBObject inc = new BasicDBObject()
          .append(EnterpriseFileStatsFieIds.usedQuota, item.getQuota())
          .append(EnterpriseFileStatsFieIds.fileCount, item.getCount());
      
      DBObject set = new BasicDBObject()
          .append(EnterpriseFileStatsFieIds.lastUpdateTime, System.currentTimeMillis());
      
      update.put("$inc", inc);
      update.put("$set", set);
      
      bulkWrite.find(query).updateOne(update);
    }

    BulkWriteResult result = bulkWrite.execute();
    if (result.getModifiedCount() != items.size()) {
      log.warn("Batch update count mismatch, expected: {}, actual: {}, items: {}",
          items.size(), result.getModifiedCount(), items);
    }
    log.debug("Batch update completed, matched: {}, modified: {}",
        result.getMatchedCount(), result.getModifiedCount());
  }

  @Override
  public EnterpriseFileStats findByEA(String ea) {
    Query<EnterpriseFileStats> query = getQuery();
    query.field(EnterpriseFileStatsFieIds.ea).equal(ea);
    return query.get();
  }

  @Override
  public Optional<EnterpriseFileStats> findByPresetZero() {
    EnterpriseFileStats result = getQuery()
        .field(EnterpriseFileStatsFieIds.preset).equal(0)
        .order(EnterpriseFileStatsFieIds.createTime) // 按创建时间排序
        .limit(1)
        .get();
    return Optional.ofNullable(result);
  }

  @Override
  public Optional<EnterpriseFileStats> findByPresetZero(List<String> eaList) {
    Query<EnterpriseFileStats> query = getQuery()
        .field(EnterpriseFileStatsFieIds.preset).equal(0)
        .order(EnterpriseFileStatsFieIds.createTime) // 按创建时间排序
        .limit(1);
    if (eaList != null && !eaList.isEmpty()) {
      query.field(EnterpriseFileStatsFieIds.ea).notIn(eaList);
    }
    EnterpriseFileStats result = query.get();
    return Optional.ofNullable(result);
  }

  @Override
  public void backfillHistoryUsedQuota(String ea, long usedQuota, long fileCount) {
    Query<EnterpriseFileStats> query = getQuery();
    query.field(EnterpriseFileStatsFieIds.ea).equal(ea)
         .field(EnterpriseFileStatsFieIds.preset).equal(0); // 确保只更新preset=0的记录
    
    UpdateOperations<EnterpriseFileStats> ops = getUpdateOperations();
    ops.inc(EnterpriseFileStatsFieIds.usedQuota, usedQuota)
        .inc(EnterpriseFileStatsFieIds.fileCount, fileCount)
        .set(EnterpriseFileStatsFieIds.preset, 1)
        .set(EnterpriseFileStatsFieIds.lastUpdateTime, System.currentTimeMillis());
    
    UpdateResults update = dataStore.update(query, ops);
    if (update == null || update.getUpdatedCount() <= 0) {
      throw new BaseException(MODULE + ".updateStatsAndSetPreset",
          "Failed to update stats and preset - record may have been already processed", ea);
    }
  }

  private Query<EnterpriseFileStats> getQuery() {
    return dataStore.createQuery(EnterpriseFileStats.class);
  }

  private UpdateOperations<EnterpriseFileStats> getUpdateOperations() {
    return dataStore.createUpdateOperations(EnterpriseFileStats.class);
  }

  private UpdateOperations<EnterpriseFileStats> createIncrementOperation(IncFileUsedQuotaItem item) {
    return getUpdateOperations()
        // 新文档的默认值
        .setOnInsert(EnterpriseFileStatsFieIds.ea, item.getEa())
        .setOnInsert(EnterpriseFileStatsFieIds.licenseQuota, 0L) // 设置默认配额
        .setOnInsert(EnterpriseFileStatsFieIds.createTime, System.currentTimeMillis()) // 设置创建时间
        .setOnInsert(EnterpriseFileStatsFieIds.version, 1) // 设置版本号
        .setOnInsert(EnterpriseFileStatsFieIds.preset, 0) // 设置预设值
        // inc原子操作如果字段不存在则创建
        .inc(EnterpriseFileStatsFieIds.usedQuota, item.getQuota())
        .inc(EnterpriseFileStatsFieIds.fileCount, item.getCount())
        .set(EnterpriseFileStatsFieIds.lastUpdateTime, System.currentTimeMillis());
  }

  private void upsertIncUsedQuota(IncFileUsedQuotaItem item) {
    // 创建查询条件
    Query<EnterpriseFileStats> query = getQuery();
    query.field(EnterpriseFileStatsFieIds.ea).equal(item.getEa());

    // 创建更新条件
    UpdateOperations<EnterpriseFileStats> ops = createIncrementOperation(item);

    // 更新
    UpdateResults update = dataStore.update(
        query,      // 查询条件
        ops,        // 更新操作
        true
    );

    // 检查是否有更新或插入操作成功
    if (update == null || (update.getUpdatedCount() <= 0 && update.getInsertedCount() <= 0)) {
      throw new BaseException(MODULE + ".upsertIncUsedQuota",
          "Failed to update used quota", item);
    }
  }

  private void IncUsedQuota(IncFileUsedQuotaItem item) {
    // 创建查询条件
    Query<EnterpriseFileStats> query = getQuery();
    query.field(EnterpriseFileStatsFieIds.ea).equal(item.getEa());
    query.retrievedFields(true, EnterpriseFileStatsFieIds.usedQuota,
        EnterpriseFileStatsFieIds.fileCount);

    // 创建更新操作
    UpdateOperations<EnterpriseFileStats> ops = getUpdateOperations();
    ops.inc(EnterpriseFileStatsFieIds.usedQuota, item.getQuota())
        .inc(EnterpriseFileStatsFieIds.fileCount, item.getCount())
        .set(EnterpriseFileStatsFieIds.lastUpdateTime, System.currentTimeMillis());
    // 执行更新 upsert 与 returnNew 默认为false 性能最好
    UpdateResults update = dataStore.update(query, ops);
    // 更新失败抛出异常
    if (update == null || update.getUpdatedCount() <= 0) {
      throw new BaseException(MODULE + ".upsertIncUsedQuota",
          "Failed to update used quota", item);
    }
  }

}
