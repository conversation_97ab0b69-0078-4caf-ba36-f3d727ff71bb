package com.fxiaoke.com.file.datax.stats.domain.entity.model;

import com.fxiaoke.com.file.datax.stats.domain.fieids.NFileMetaFieIds;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class NChunkSubFiles {

  @Property(NFileMetaFieIds.nChunkSubFiles_chunkSubFileSize)
  private int chunkSubFileSize;
  @Property(NFileMetaFieIds.nChunkSubFiles_lastChunkSubFileSize)
  private int lastChunkSubFileSize;
  @Property(NFileMetaFieIds.nChunkSubFiles_chunkSubFileCount)
  private int chunkSubFileCount;
  @Embedded(NFileMetaFieIds.nChunkSubFiles_nChunkSubFiles)
  private List<NChunkSubFile> nChunkSubFiles;
}
