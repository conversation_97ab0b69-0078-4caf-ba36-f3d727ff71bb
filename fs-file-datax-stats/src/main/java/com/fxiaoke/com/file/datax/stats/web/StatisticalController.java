package com.fxiaoke.com.file.datax.stats.web;

import com.fxiaoke.com.file.datax.stats.service.StatisticalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/FilesOne/Statistical")
public class StatisticalController {

  private final StatisticalService statisticalService;

  public StatisticalController(StatisticalService statisticalService) {
    this.statisticalService = statisticalService;
  }

  @GetMapping("/backfillHistoryStats")
  public String backfillHistoryStats(){
    log.info("Received request for backfill history stats, executing asynchronously");

    ExecutorService executorService = Executors.newSingleThreadExecutor();

    CompletableFuture.runAsync(() -> {
      try {
        log.info("Starting backfill history stats in async thread: {}", Thread.currentThread().getName());
        statisticalService.backfillHistoryStats();
        log.info("Backfill history stats completed successfully");
      } catch (Exception e) {
        log.error("Backfill history stats failed in async thread", e);
        throw new RuntimeException(e);
      }
    }, executorService);
    return "backfillHistoryStats Started";
  }

  @GetMapping(value = "/backfillHistoryStatsByEa",params = "ea")
  public String backfillHistoryStatsByEa(String ea){
    log.info("Received request for backfill history stats by EA: {}", ea);
    ExecutorService executorService = Executors.newSingleThreadExecutor();
    CompletableFuture.runAsync(() -> {
      try {
        log.info("Starting backfill history stats by EA in async thread: {}, EA: {}", Thread.currentThread().getName(), ea);
        statisticalService.backfillHistoryStatsByEa(ea);
        log.info("Backfill history stats by EA completed successfully for EA: {}", ea);
      } catch (Exception e) {
        log.error("Backfill history stats by EA failed in async thread for EA: {}", ea, e);
        throw new RuntimeException(e);
      }
    }, executorService);
    return "backfillHistoryStatsByEa Started";
  }
}
