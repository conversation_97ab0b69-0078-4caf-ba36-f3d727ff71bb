package com.fxiaoke.com.file.datax.stats.service;


import com.fxiaoke.com.file.datax.stats.domain.model.EnterpriseBaseInfo;
import java.util.Date;

public interface AsmService {

  /**
   * 根据企业账号获取企业ID
   *
   * @param ea 企业账号
   * @return 企业ID
   */
  int getEid(String ea);

  /**
   * 根据企业ID获取企业账号
   *
   * @param eid 企业ID
   * @return 企业账号
   */
  String getEa(int eid);


  /**
   * 获取企业创建时间
   *
   * @param ea 企业账号
   * @return 企业创建时间
   */
  Date getCreateTime(String ea);

  /**
   * 获取企业的基本信息
   *
   * @param ea 企业账号
   * @return 企业基本信息
   */
  EnterpriseBaseInfo getBaseEnterpriseInfo(String ea);

  /**
   * 判断当前用户是否是当前云的用户
   *
   * @param tenantId 租户ID
   * @return true 如果是当前云的用户，否则 false
   */
  boolean isCurrentCloudUser(String tenantId);
}
