package com.fxiaoke.com.file.datax.stats.exception;

import com.fxiaoke.com.file.datax.stats.domain.R;
import com.fxiaoke.com.file.datax.stats.domain.constants.ErrorCodes;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.github.trace.TraceContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.UnsatisfiedServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * 全局异常处理器 统一处理所有类型的异常，提供一致的错误响应格式
 */
@RestControllerAdvice
@Slf4j(topic = "GlobalExceptionHandler")
public class GlobalExceptionHandler {

  private static final MediaType DEFAULT_CONTENT_TYPE = MediaType.APPLICATION_JSON;

  /**
   * 处理业务基础异常
   */
  @ExceptionHandler(BaseException.class)
  public ResponseEntity<R<String>> handleBaseException(BaseException e,
      HttpServletRequest request) {
    logBusinessException(e, request);
    return buildErrorResponse(e.getCode(), e.getMessage());
  }

  /**
   * 处理客户端连接中断异常
   */
  @ExceptionHandler(ClientAbortException.class)
  public void handleClientAbortException(ClientAbortException e, HttpServletResponse response,
      HttpServletRequest request) {
    logWarn("Client connection aborted: {}", e.getMessage(), request);
    response.setStatus(ErrorCodes.HTTP_CLIENT_CLOSED_REQUEST);
  }

  /**
   * 处理异步请求超时异常
   */
  @ExceptionHandler(AsyncRequestTimeoutException.class)
  public ResponseEntity<R<String>> handleAsyncRequestTimeoutException(
      AsyncRequestTimeoutException e, HttpServletRequest request) {
    logError("Async request timeout", e, request);
    return buildErrorResponse(ErrorCodes.HTTP_INTERNAL_SERVER_ERROR, ErrorCodes.ASYNC_TIMEOUT_MSG);
  }

  /**
   * 处理参数验证异常 - BindException
   */
  @ExceptionHandler(BindException.class)
  public ResponseEntity<R<String>> handleBindException(BindException e,
      HttpServletRequest request) {
    logWarn("Parameter validation failed: {}", e.getMessage(), request);
    String errorMessage = e.getAllErrors().stream()
        .map(DefaultMessageSourceResolvable::getDefaultMessage)
        .collect(Collectors.joining("; "));
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, errorMessage);
  }

  /**
   * 处理参数验证异常 - MethodArgumentNotValidException
   */
  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<R<String>> handleMethodArgumentNotValidException(
      MethodArgumentNotValidException e, HttpServletRequest request) {
    logWarn("Method argument validation failed: {}", e.getMessage(), request);
    String errorMessage = e.getBindingResult().getAllErrors().stream()
        .map(DefaultMessageSourceResolvable::getDefaultMessage)
        .collect(Collectors.joining("; "));
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, errorMessage);
  }

  /**
   * 处理约束违反异常
   */
  @ExceptionHandler(ConstraintViolationException.class)
  public ResponseEntity<R<String>> handleConstraintViolationException(
      ConstraintViolationException e, HttpServletRequest request) {
    logWarn("Constraint violation: {}", e.getMessage(), request);
    String errorMessage = e.getConstraintViolations().stream()
        .map(ConstraintViolation::getMessage)
        .collect(Collectors.joining("; "));
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, errorMessage);
  }

  /**
   * 处理缺少请求头异常
   */
  @ExceptionHandler(MissingRequestHeaderException.class)
  public ResponseEntity<R<String>> handleMissingRequestHeaderException(
      MissingRequestHeaderException e, HttpServletRequest request) {
    logWarn("Missing request header: {}", e.getHeaderName(), request);
    String errorMessage = "Request header parameter is missing: " + e.getHeaderName();
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, errorMessage);
  }

  /**
   * 处理请求体不可读异常
   */
  @ExceptionHandler(HttpMessageNotReadableException.class)
  public ResponseEntity<R<String>> handleHttpMessageNotReadableException(
      HttpMessageNotReadableException e, HttpServletRequest request) {
    logWarn("HTTP message not readable: {}", e.getMessage(), request);
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, ErrorCodes.REQUIRED_BODY_MISSING_MSG);
  }

  /**
   * 处理缺少请求参数异常
   */
  @ExceptionHandler(MissingServletRequestParameterException.class)
  public ResponseEntity<R<String>> handleMissingServletRequestParameterException(
      MissingServletRequestParameterException e, HttpServletRequest request) {
    logWarn("Missing request parameter: {}", e.getParameterName(), request);
    String errorMessage = "Required request parameter is missing: " + e.getParameterName();
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, errorMessage);
  }

  /**
   * 处理请求参数不满足条件异常
   */
  @ExceptionHandler(UnsatisfiedServletRequestParameterException.class)
  public ResponseEntity<R<String>> handleUnsatisfiedServletRequestParameterException(
      UnsatisfiedServletRequestParameterException e, HttpServletRequest request) {
    logWarn("Unsatisfied request parameter condition: {}", e.getMessage(), request);
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, e.getMessage());
  }

  /**
   * 处理方法参数类型不匹配异常
   */
  @ExceptionHandler(MethodArgumentTypeMismatchException.class)
  public ResponseEntity<R<String>> handleMethodArgumentTypeMismatchException(
      MethodArgumentTypeMismatchException e, HttpServletRequest request) {
    logWarn("Method argument type mismatch: {}", e.getMessage(), request);
    String errorMessage = String.format("Parameter '%s' should be of type '%s'",
        e.getName(), e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "unknown");
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, errorMessage);
  }

  /**
   * 处理文件上传大小超限异常
   */
  @ExceptionHandler(MaxUploadSizeExceededException.class)
  public ResponseEntity<R<String>> handleMaxUploadSizeExceededException(
      MaxUploadSizeExceededException e, HttpServletRequest request) {
    logWarn("File upload size exceeded: {}", e.getMessage(), request);
    return buildErrorResponse(ErrorCodes.HTTP_PAYLOAD_TOO_LARGE, ErrorCodes.FILE_SIZE_EXCEEDED_MSG);
  }

  /**
   * 处理非法状态异常
   */
  @ExceptionHandler(IllegalStateException.class)
  public ResponseEntity<R<String>> handleIllegalStateException(IllegalStateException e,
      HttpServletRequest request) {
    logWarn("Illegal state: {}", e.getMessage(), request);
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, e.getMessage());
  }

  /**
   * 处理非法参数异常
   */
  @ExceptionHandler(IllegalArgumentException.class)
  public ResponseEntity<R<String>> handleIllegalArgumentException(IllegalArgumentException e,
      HttpServletRequest request) {
    logWarn("Illegal argument: {}", e.getMessage(), request);
    return buildErrorResponse(ErrorCodes.HTTP_BAD_REQUEST, e.getMessage());
  }

  /**
   * 处理所有未捕获的异常 - 兜底处理
   */
  @ExceptionHandler(Exception.class)
  public ResponseEntity<R<String>> handleGenericException(Exception e, HttpServletRequest request) {
    logError("Unhandled exception occurred", e, request);
    return buildErrorResponse(ErrorCodes.HTTP_INTERNAL_SERVER_ERROR, ErrorCodes.UNKNOWN_ERROR_MSG);
  }

  // ===== 私有辅助方法 =====

  /**
   * 构建错误响应
   */
  private ResponseEntity<R<String>> buildErrorResponse(int statusCode, String message) {
    return ResponseEntity.status(statusCode)
        .contentType(DEFAULT_CONTENT_TYPE)
        .body(R.error(statusCode, message));
  }

  /**
   * 记录业务异常日志 - BaseException
   */
  private void logBusinessException(BaseException e, HttpServletRequest request) {
    String traceId = getTraceId();
    String requestInfo = buildRequestInfo(request);

    if (e.getCode() < ErrorCodes.HTTP_INTERNAL_SERVER_ERROR) {
      log.warn("Business exception occurred - traceId: {}, code: {}, reason: {}, request: {}",
          traceId, e.getCode(), e.getReason(), requestInfo, e);
    } else {
      log.error("Business exception occurred - traceId: {}, code: {}, reason: {}, request: {}",
          traceId, e.getCode(), e.getReason(), requestInfo, e);
    }
  }

  /**
   * 记录业务异常日志 - 自定义异常
   */
  private void logBusinessException(int code, String reason, Exception e,
      HttpServletRequest request) {
    String traceId = getTraceId();
    String requestInfo = buildRequestInfo(request);

    if (code < ErrorCodes.HTTP_INTERNAL_SERVER_ERROR) {
      log.warn("Business exception occurred - traceId: {}, code: {}, reason: {}, request: {}",
          traceId, code, reason, requestInfo, e);
    } else {
      log.error("Business exception occurred - traceId: {}, code: {}, reason: {}, request: {}",
          traceId, code, reason, requestInfo, e);
    }
  }

  /**
   * 记录警告级别日志
   */
  private void logWarn(String message, Object arg, HttpServletRequest request) {
    String traceId = getTraceId();
    String requestInfo = buildRequestInfo(request);
    log.warn("Client exception - traceId: {}, message: {}, arg: {}, request: {}",
        traceId, message, arg, requestInfo);
  }

  /**
   * 记录错误级别日志
   */
  private void logError(String message, Exception e, HttpServletRequest request) {
    String traceId = getTraceId();
    String requestInfo = buildRequestInfo(request);
    log.error("Server exception - traceId: {}, message: {}, request: {}",
        traceId, message, requestInfo, e);
  }

  /**
   * 获取当前请求的追踪ID
   */
  private String getTraceId() {
    try {
      String traceId = TraceContext.get().getTraceId();
      return traceId != null ? traceId : UUID.randomUUID().toString().replace("-", "");
    } catch (Exception e) {
      return UUID.randomUUID().toString().replace("-", "");
    }
  }

  /**
   * 构建请求信息字符串
   */
  private String buildRequestInfo(HttpServletRequest request) {
    if (request == null) {
      return "unknown";
    }
    return String.format("%s %s", request.getMethod(), request.getRequestURI());
  }
}
