package com.fxiaoke.com.file.datax.stats.domain.model;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.time.FastDateFormat;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class EnterpriseBaseInfo {
  private String enterpriseAccount;
  private String tenantId;
  private Date createTime;
  private String dfsAddress;

  public String getCreateTimeStr(){
    return FastDateFormat.getInstance("yyyyMM", null, null).format(createTime);
  }
}
