package com.fxiaoke.com.file.datax.stats.service.impl;

import com.fxiaoke.com.file.datax.stats.config.CmsPropertiesConfig;
import com.fxiaoke.com.file.datax.stats.dao.DataBaseManager;
import com.fxiaoke.com.file.datax.stats.dao.EnterpriseFileStatsDao;
import com.fxiaoke.com.file.datax.stats.dao.NFileMetaDao;
import com.fxiaoke.com.file.datax.stats.dao.RedisOperator;
import com.fxiaoke.com.file.datax.stats.domain.entity.EnterpriseFileStats;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.fxiaoke.com.file.datax.stats.domain.model.*;
import com.fxiaoke.com.file.datax.stats.service.AsmService;
import com.fxiaoke.com.file.datax.stats.service.StatisticalService;
import com.fxiaoke.com.file.datax.stats.utils.DBUtil;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StatisticalServiceImpl implements StatisticalService {

  private static final String MODULE = "EnterpriseFileStatsImpl";

  private final CmsPropertiesConfig config;

  private final AsmService asmService;
  private final NFileMetaDao nFileMetaDao;
  private final RedisOperator redisOperator;
  private final DataBaseManager dataBaseManager;
  private final EnterpriseFileStatsDao enterpriseFileStatsDao;


  public StatisticalServiceImpl(CmsPropertiesConfig cmsPropertiesConfig, AsmService asmService,
      NFileMetaDao nFileMetaDao, RedisOperator redisOperator, DataBaseManager dataBaseManager,
      EnterpriseFileStatsDao enterpriseFileStatsDao) {
    this.config = cmsPropertiesConfig;
    this.asmService = asmService;
    this.nFileMetaDao = nFileMetaDao;
    this.redisOperator = redisOperator;
    this.dataBaseManager = dataBaseManager;
    this.enterpriseFileStatsDao = enterpriseFileStatsDao;
  }

  @Override
  public void incFileUsedQuota(IncFileUsedQuotaItem item) {
    log.debug("incFileUsedQuota: {}", item);
    enterpriseFileStatsDao.incUsedQuota(item);
  }

  @Override
  public void incFileUsedQuotas(List<IncFileUsedQuotaItem> items) {
    List<IncFileUsedQuotaItem> mergeQuotaItems = mergeQuotaItems(items);
    log.debug("incFileUsedQuotas: {}", mergeQuotaItems);
    enterpriseFileStatsDao.incUsedQuotas(mergeQuotaItems);
  }

  /**
   * 合并配额项，按EA分组
   *
   * @param incFileQuotaItems 待合并的配额项列表
   * @return 合并后的配额项列表
   * @throws BaseException 如果EA为空或其他参数错误
   */
  private List<IncFileUsedQuotaItem> mergeQuotaItems(List<IncFileUsedQuotaItem> incFileQuotaItems) {
    // 使用HashMap即可，无需LinkedHashMap
    Map<String, IncFileUsedQuotaItem> itemMap = new HashMap<>(incFileQuotaItems.size());

    for (IncFileUsedQuotaItem item : incFileQuotaItems) {
      String ea = item.getEa();
      if (Strings.isNullOrEmpty(ea)) {
        throw new BaseException(MODULE + ".mergeQuotaItems", 400,
            "ea parameter in incFileQuotaItems can not be blank", incFileQuotaItems);
      }

      long quota = item.getQuota();
      long fileCount = quota > 0 ? 1L : -1L;

      itemMap.merge(ea,
          new IncFileUsedQuotaItem(ea, quota, fileCount),
          (existing, newItem) -> {
            existing.setQuota(existing.getQuota() + newItem.getQuota());
            existing.setCount(existing.getCount() + newItem.getCount());
            return existing;
          });
    }

    return new ArrayList<>(itemMap.values());
  }


  private volatile boolean backfillRunning;

  private volatile boolean backfillEaRunning;

  private static final String LOCK_BIZ = "backfillHistoryStats";

  private static final Cache<String,Boolean> EA_LOCK_CACHE = CacheBuilder.newBuilder()
      .maximumSize(10000)
      .expireAfterWrite(1, TimeUnit.HOURS)
      .build();

  /**
   * 补充统计历史文件用量配额 使用分布式锁避免重复处理，支持批量处理和异常重试
   */
  @Override
  public void backfillHistoryStats() {

    // 检查是否启用历史统计回填
    if (!config.isEnableBackfillHistoryStats()) {
      log.info("Backfill history stats is disabled, skipping");
      return;
    }

    try {

      // 更改运行状态
      if (backfillRunning) {
        log.warn("Backfill history stats is already running, skipping this invocation");
        return;
      }

      while (config.isRuntimeBackfillHistoryStats()) {

        backfillRunning = true;

        List<String> ignoreEAs = new ArrayList<>(EA_LOCK_CACHE.asMap().keySet());
        // 批量查询待处理的企业记录
        Optional<EnterpriseFileStats> statsRecordOpt = enterpriseFileStatsDao.findByPresetZero(ignoreEAs);

        // 如果没有记录，结束回填
        if (statsRecordOpt.isEmpty()) {
          log.info("No statsRecords found for backfill, exiting");
          break;
        }

        EnterpriseFileStats record = statsRecordOpt.get();
        // 判断是否有锁,有锁跳过,无锁加锁
        Optional<String> lockValueOpt = redisOperator.tryLock(record.getEa(), LOCK_BIZ, config.getBackfillLockTimeout());
        if (lockValueOpt.isEmpty()) {
          EA_LOCK_CACHE.put(record.getEa(), Boolean.TRUE);
          log.info("No lock acquired for ea={}, skipping this record", record.getEa());
          continue; // 没有获取到锁，跳过当前记录
        }
        String lockValue = lockValueOpt.get();

        try {
          log.info("Acquired lock for ea={}, processing record", record.getEa());

          // 统计企业创建到当前时间范围的文件总大小与文件总数
          EnterpriseFileSizeCount stats = statsFileHistoryUsedQuota(record.getEa(),
              new Date(record.getCreateTime()));
          // 补充历史文件用量配额
          enterpriseFileStatsDao.backfillHistoryUsedQuota(record.getEa(), stats.getTotalSize(),
              stats.getCount());

          log.info("Updated stats for ea={}, totalSize={}, count={}", record.getEa(),
              stats.getTotalSize(), stats.getCount());
        } finally {
          // 释放锁
          redisOperator.releaseLock(record.getEa(), LOCK_BIZ, lockValue);
        }

        log.info("backfillHistoryStats success,ea:{}", record.getEa());
      }
    } catch (Exception e) {
      throw new BaseException(MODULE + ".backfillHistoryStats", 500, "Error during backfill", e);
    }finally {
      EA_LOCK_CACHE.cleanUp();
      // 重置运行状态
      backfillRunning = false;
    }
  }

  @Override
  public void backfillHistoryStatsByEa(String ea){
    log.info("backfillHistoryStatsByEa: ea={}", ea);

    // 检查是否启用历史统计回填
    if (!config.isEnableBackfillHistoryStats()) {
      log.info("Backfill history stats is disabled, skipping for ea={}", ea);
      return;
    }

    if (backfillEaRunning) {
      log.warn("Backfill history stats is already running, skipping");
      return;
    }

    // 判断是否有锁，有锁跳过，无锁加锁
    Optional<String> lockValueOpt = redisOperator.tryLock(ea, LOCK_BIZ, config.getBackfillLockTimeout());
    if (lockValueOpt.isEmpty()) {
      log.info("No lock acquired for ea={}, skipping", ea);
      return;
    }
    String lockValue = lockValueOpt.get();


    try {

      backfillEaRunning = true;

      log.info("Acquired lock for ea={}, processing", ea);

      // 查询企业统计记录
      EnterpriseFileStats record = enterpriseFileStatsDao.findByEA(ea);
      if (record==null) {
        log.info("No stats record found for ea={}, skipping", ea);
        return;
      }

      // 统计企业创建到当前时间范围的文件总大小与文件总数
      EnterpriseFileSizeCount stats = statsFileHistoryUsedQuota(ea, new Date(record.getCreateTime()));

      // 补充历史文件用量配额
      enterpriseFileStatsDao.backfillHistoryUsedQuota(ea, stats.getTotalSize(), stats.getCount());

      log.info("Single Updated stats for ea={}, totalSize={}, count={}", ea, stats.getTotalSize(), stats.getCount());
    } catch (Exception e) {
      log.error("Error during backfill for ea={}", ea, e);
      throw new BaseException(MODULE + ".backfillHistoryStatsByEa", 500, "Error during backfill for ea: " + ea, e);
    } finally {
      // 释放锁
      redisOperator.releaseLock(ea, LOCK_BIZ, lockValue);
      backfillEaRunning = false;
      log.info("backfillHistoryStatsByEa success for ea={}", ea);
    }
  }


  /**
   * 统计企业创建到指定时间范围的文件总大小与文件总数。
   * 1. 获取在写DB
   * 2. 获取企业创建时间
   * 3. 根据企业创建时间与输入最终结束时间创建时间分片
   * 4. 根据时间分片分批并根据在写DB查询文件数据计算每批文件总大小
   * 5. 汇聚文件数据计算文件总大小与文件总数
   * 6. 返回文件总大小与文件总数
   * @param ea 企业标识
   * @param endDate 结束时间
   * @return 企业文件大小统计数据
   */
  @Override
  public EnterpriseFileSizeCount statsFileHistoryUsedQuota(String ea, Date endDate) {
    log.info("backfillHistoryStats: ea={}, endDate={}", ea, endDate);
    // 1. 获取在写DB
    DatastoreExt readDataStore = dataBaseManager.getReadDataStore(ea);
    // 2. 获取企业创建时间
    Date startDate = asmService.getCreateTime(ea);
    if (endDate == null || endDate.after(new Date())) {
      endDate = new Date();
    }
    // 3. 根据当前时间和企业创建时间创建时间分片
    List<MongoObjectIdTimeSlice> secTimeSlice = DBUtil.getSecTimeSlice(startDate, endDate, config.getBackfillHistoryStatsMinPart());

    AtomicLong totalSize = new AtomicLong(0L);
    AtomicLong totalCount = new AtomicLong(0L);

    // 4. 根据时间分片分批并根据在写DB查询文件数据计算每批文件总大小
    for (MongoObjectIdTimeSlice slice : secTimeSlice) {
      // 如果配置停止运行回填历史统计，则抛出异常立即停止
      if (!config.isRuntimeBackfillHistoryStats()){
        log.info("Backfill history stats is disabled, stopping processing");
        break;
      }
      nFileMetaDao.statisticalFileSizeCount(readDataStore, ea, slice)
          .ifPresent(enterpriseFileSizeCount -> {
            // 5. 汇聚文件数据计算文件总大小与文件总数
            totalSize.addAndGet(enterpriseFileSizeCount.getTotalSize());
            totalCount.addAndGet(enterpriseFileSizeCount.getCount());
          });
    }
    log.info("backfillHistoryStats: totalSize={}, totalCount={}", totalSize.get(), totalCount.get());
    return new EnterpriseFileSizeCount(totalSize.get(), totalCount.get());
  }
}
