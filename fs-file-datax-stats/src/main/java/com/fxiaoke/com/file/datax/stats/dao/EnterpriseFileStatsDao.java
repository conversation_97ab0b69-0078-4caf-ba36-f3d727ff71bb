package com.fxiaoke.com.file.datax.stats.dao;

import com.fxiaoke.com.file.datax.stats.domain.entity.EnterpriseFileStats;
import com.fxiaoke.com.file.datax.stats.domain.model.IncFileUsedQuotaItem;
import java.util.List;
import java.util.Optional;

public interface EnterpriseFileStatsDao {

  /**
   * 原子性累计已使用配额、文件数量
   */
  void incUsedQuota(IncFileUsedQuotaItem item);

  /**
   * 批量原子性累计已使用配额、文件数量
   */
  void incUsedQuotas(List<IncFileUsedQuotaItem> items);

  /**
   * 查看已使用配额、文件数量
   */
  EnterpriseFileStats findByEA(String ea);

  /**
   * 查询preset值为0的企业记录
   * @return 企业记录列表
   */
  Optional<EnterpriseFileStats> findByPresetZero();

  Optional<EnterpriseFileStats> findByPresetZero(List<String> eaList);

  /**
   * 更新企业的已使用配额和文件数量，并将preset设置为1
   */
  void backfillHistoryUsedQuota(String ea, long usedQuota, long fileCount);

}
