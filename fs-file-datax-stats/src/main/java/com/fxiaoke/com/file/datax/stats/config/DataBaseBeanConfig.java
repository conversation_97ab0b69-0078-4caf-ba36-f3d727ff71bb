package com.fxiaoke.com.file.datax.stats.config;

import com.fxiaoke.com.file.datax.stats.config.condition.ConditionalOnDataStore;
import com.github.jedis.support.JedisFactoryBean;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataBaseBeanConfig {

  @Bean
  public JedisFactoryBean dataxRedis(CmsPropertiesConfig config) {
    JedisFactoryBean jedisFactoryBean = new JedisFactoryBean();
    jedisFactoryBean.setConfigName(config.getDataxRedisConfigName());
    return jedisFactoryBean;
  }

  @Bean
  public MongoDataStoreFactoryBean warehouseDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getWarehouseMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  @ConditionalOnDataStore(firstShareDataStore = true)
  public MongoDataStoreFactoryBean nDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getNMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  @ConditionalOnDataStore()
  public MongoDataStoreFactoryBean n1DataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getN1MongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  @ConditionalOnDataStore()
  public MongoDataStoreFactoryBean n2DataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getN2MongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  public MongoDataStoreFactoryBean shardDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getShardMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

}
