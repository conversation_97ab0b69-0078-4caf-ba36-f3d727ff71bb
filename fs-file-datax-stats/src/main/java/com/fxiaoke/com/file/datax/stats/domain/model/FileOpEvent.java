package com.fxiaoke.com.file.datax.stats.domain.model;

import com.fxiaoke.com.file.datax.stats.domain.constants.Operation;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class FileOpEvent {

  private String enterpriseAccount;
  private String sourceUser;
  private long size;
  private Operation operation;
  private String business;
  private String fileExtension;
  private String fileName;
  private boolean image;
  private String ip;
  private String caller;
  private Date operateDate;
  private boolean chunkFile;
  private String masterFile;
  private String master;
  private String group;
  private boolean bigStorage;
  private boolean gc;
  private boolean thumbnail;
  private boolean tempFile;
  private long statisticsTimestamp;
  private long expiredTimeStamp;
  private String code;
  private boolean cloudStorage;
  private String objectKey;
  private String bucket;
  private String cloudType;
  private String year;
  private String month;
  private String day;
  private String hour;
  private String minute;
  private int quarter;
  private int weekday;
  private String processName;

  public StringBuilder toLog() {
    return new StringBuilder()
        .append("ea: ").append(enterpriseAccount)
        .append(", user: ").append(sourceUser).append('\'')
        .append(", size: ").append(size)
        .append(", operation: ").append(operation)
        .append(", business: ").append(business).append('\'')
        .append(", ext='").append(fileExtension).append('\'')
        .append(", path='").append(fileName).append('\'')
        .append(", caller='").append(caller).append('\'');
  }
}
