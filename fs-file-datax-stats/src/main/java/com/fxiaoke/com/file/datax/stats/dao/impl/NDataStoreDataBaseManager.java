package com.fxiaoke.com.file.datax.stats.dao.impl;


import com.fxiaoke.com.file.datax.stats.config.condition.ConditionalOnDataStore;
import com.fxiaoke.com.file.datax.stats.dao.StoneCoreMetaDao;
import com.fxiaoke.com.file.datax.stats.domain.constants.DataBaseCluster;
import com.fxiaoke.com.file.datax.stats.domain.constants.ErInfo;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.fxiaoke.com.file.datax.stats.domain.model.StoneCoreConfig;
import com.fxiaoke.com.file.datax.stats.service.impl.AsmServiceImpl;
import com.github.mongo.support.DatastoreExt;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnDataStore(firstShareDataStore = true)
public class NDataStoreDataBaseManager extends CommonDataBaseManager{

  protected static final String MODULE = "NDataStoreDataBaseManager";

  @Resource
  private DatastoreExt nDataStore;

  @Resource
  private DatastoreExt shardDataStore;

  public NDataStoreDataBaseManager(
      AsmServiceImpl asmServiceImpl,
      StoneCoreMetaDao stoneCoreMetaDao) {
    super(asmServiceImpl, stoneCoreMetaDao);
  }

  @Override
  public DatastoreExt getReadDataStore(String ea) {
    return getEnterpriseReadDataStore(ea);
  }

  private DatastoreExt getEnterpriseReadDataStore(String ea) {
    StoneCoreConfig stoneCoreConfig = loadStoneCoreConfig(ea);
    DataBaseCluster cluster = stoneCoreConfig.getCluster();
    return switch (cluster) {
      case N -> nDataStore.use(stoneCoreConfig.getDbName());
      case Shard -> shardDataStore.setTenantId(stoneCoreConfig.getTenantId());
      default ->
        // 如果不是N集群或分片集群，则抛出异常
          throw new BaseException(MODULE, ErInfo.DATABASE_NO_ROUTE, ea);
    };
  }

}
