package com.fxiaoke.com.file.datax.stats.dao.impl;

import com.fxiaoke.com.file.datax.stats.dao.DataBaseManager;

import com.fxiaoke.com.file.datax.stats.dao.StoneCoreMetaDao;
import com.fxiaoke.com.file.datax.stats.domain.constants.DataBaseCluster;
import com.fxiaoke.com.file.datax.stats.domain.constants.DataBaseTransferStatus;
import com.fxiaoke.com.file.datax.stats.domain.constants.ErInfo;
import com.fxiaoke.com.file.datax.stats.domain.entity.StoneCoreMeta;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.fxiaoke.com.file.datax.stats.domain.model.EnterpriseBaseInfo;
import com.fxiaoke.com.file.datax.stats.domain.model.StoneCoreConfig;
import com.fxiaoke.com.file.datax.stats.service.impl.AsmServiceImpl;
import java.util.Optional;

public abstract class CommonDataBaseManager implements DataBaseManager {

  protected static final String MODULE = "CommonDataBaseManager";

  protected final AsmServiceImpl asmServiceImpl;
  protected final StoneCoreMetaDao stoneCoreMetaDao;

  public CommonDataBaseManager(AsmServiceImpl asmServiceImpl,
      StoneCoreMetaDao stoneCoreMetaDao) {
    this.stoneCoreMetaDao = stoneCoreMetaDao;
    this.asmServiceImpl = asmServiceImpl;
  }

  protected StoneCoreConfig loadStoneCoreConfig(String ea) {

    // 查找文件系统核心路由表
    Optional<StoneCoreMeta> stoneCoreMetaOpt = stoneCoreMetaDao.find(ea);
    if (stoneCoreMetaOpt.isEmpty()) {
      throw new BaseException(MODULE, ErInfo.STONE_CORE_META_NOT_FOUND, ea);
    }
    StoneCoreMeta stoneCoreMeta = stoneCoreMetaOpt.get();

    StoneCoreConfig stoneCoreConfig = new StoneCoreConfig();
    EnterpriseBaseInfo baseEnterpriseInfo = asmServiceImpl.getBaseEnterpriseInfo(ea);
    stoneCoreConfig.setEa(ea);
    stoneCoreConfig.setTenantId(baseEnterpriseInfo.getTenantId());
    String dfsAddress = baseEnterpriseInfo.getDfsAddress();
    String createTimeStr = baseEnterpriseInfo.getCreateTimeStr();
    DataBaseTransferStatus transferStatus = DataBaseTransferStatus.of(
        stoneCoreMeta.getTransferStatus());

    switch (transferStatus) {
      case NOT_STARTED, IN_PROGRESS -> {
        stoneCoreConfig.setDataBaseClusterByDfsAddress(dfsAddress);
        stoneCoreConfig.setDbNameByCreateTime(createTimeStr);
      }
      case COMPLETED -> stoneCoreConfig.setCluster(DataBaseCluster.Shard);
      default ->
          throw new BaseException(MODULE, ErInfo.STONE_CORE_META_TRANSFER_STATUS_ERROR, ea,
              transferStatus);
    }

    return stoneCoreConfig;
  }
}
