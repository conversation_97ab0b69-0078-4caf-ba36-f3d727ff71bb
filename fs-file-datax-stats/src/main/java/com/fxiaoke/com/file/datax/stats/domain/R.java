package com.fxiaoke.com.file.datax.stats.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/*
 * 统一返回结果类
 * @param <T> 返回数据类型 T
 * success 是否成功
 * code 返回码
 * message 返回信息
 * data 返回数据
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class R<T> {

  private boolean success;
  private Integer code;
  private String message;
  private T data;

  private R(boolean success, Integer code, String message) {
    this.success = success;
    this.code = code;
    this.message = message;
  }

  private R(boolean success, Integer code, String message, T data) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public static <T> R<T> ok(T data) {
    return new R<>(true, 200, "success", data);
  }
  public static <T> R<T> fail(T data,String message) {
    return new R<>(false, 400, message,data);
  }
  public static R<String> error(int code, String message) {
    return new R<>(false, code, message);
  }

}