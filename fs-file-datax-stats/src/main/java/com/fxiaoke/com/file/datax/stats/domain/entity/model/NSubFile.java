package com.fxiaoke.com.file.datax.stats.domain.entity.model;

import com.fxiaoke.com.file.datax.stats.domain.fieids.NFileMetaFieIds;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class NSubFile {

  @Property(NFileMetaFieIds.nSubFiles_index)
  public int index;

  @Property(NFileMetaFieIds.nSubFiles_type)
  public String type;

  @Property(NFileMetaFieIds.nSubFiles_masterId)
  public String masterId;

  @Property(NFileMetaFieIds.nSubFiles_group)
  public String group;

  @Property(NFileMetaFieIds.nSubFiles_storageIp)
  public String storageIp;
}
