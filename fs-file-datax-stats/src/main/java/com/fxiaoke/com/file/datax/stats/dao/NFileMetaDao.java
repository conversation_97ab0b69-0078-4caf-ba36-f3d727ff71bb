package com.fxiaoke.com.file.datax.stats.dao;


import com.fxiaoke.com.file.datax.stats.domain.constants.PathType;
import com.fxiaoke.com.file.datax.stats.domain.entity.NFileMeta;
import com.fxiaoke.com.file.datax.stats.domain.model.EnterpriseFileSizeCount;
import com.fxiaoke.com.file.datax.stats.domain.model.MongoObjectIdTimeSlice;
import java.util.Optional;
import org.mongodb.morphia.Datastore;

public interface NFileMetaDao {

  /**
   * 根据路径类型、企业账号和路径查找文件元数据
   *
   * @param db 数据库连接
   * @param pathType 路径类型
   * @param ea 企业账号
   * @param path 文件路径
   * @return 返回文件元数据的可选值，如果不存在则返回空
   */
  Optional<NFileMeta> find(Datastore db, PathType pathType, String ea, String path);

  /**
   * 根据 ObjectId 时间片 统计文件大小和数量
   *
   * @param db 数据库连接
   * @param ea 企业账号
   * @param slice 时间分片
   * @return 返回统计结果，包含文件总大小和文件总数
   */
  Optional<EnterpriseFileSizeCount> statisticalFileSizeCount(Datastore db, String ea,
      MongoObjectIdTimeSlice slice);
}
