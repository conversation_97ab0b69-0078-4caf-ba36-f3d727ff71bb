package com.fxiaoke.com.file.datax.stats.domain.constants;

import lombok.Getter;

@Getter
public enum ErInfo {

  SERVER_INIT_ERROR(500, "Server initialization error", "Server init error"),

  REMOTE_SERVICE_EXCEPTION(500, "Service exception,please contact administrator", "Remote service exception"),

  STONE_CORE_META_NOT_FOUND(500, "Enterprise file system database core route is missing",
      "StoneCore table registration information for this business is missing"),

  DATABASE_MANAGER_PATH_TYPE_NOT_SUPPORT(500,
      "DatabaseManager path type not support", "DatabaseManager path type not support"),

  DATABASE_NO_ROUTE(500, "Database route not found",
      "Database route not found for this enterprise"),

  DATABASE_INC_QUOTA_FAIL(500,"Failed to increment used quota","DataBaseUpdate: Failed to increment used quota"),

  REDIS_TRY_LOCK_FAIL(500, "Failed to acquire distributed lock","Redis try lock failed"),
  REDIS_RELEASE_LOCK_FAIL(500, "Failed to acquire distributed lock","Redis try lock failed"),
  STONE_CORE_META_TRANSFER_STATUS_ERROR(
      500, "Enterprise file system database core route status error",
          "StoneCore Transfer status error"),



  ;

  private final int code;
  private final String message;
  private final String reason;
  ErInfo(int code, String message, String reason) {
    this.code = code;
    this.message = message;
    this.reason = reason;
  }
}
