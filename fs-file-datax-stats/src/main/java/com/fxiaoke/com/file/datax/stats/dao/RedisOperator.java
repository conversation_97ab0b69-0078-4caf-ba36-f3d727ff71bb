package com.fxiaoke.com.file.datax.stats.dao;

import com.fxiaoke.com.file.datax.stats.domain.constants.ErInfo;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.github.jedis.support.MergeJedisCmd;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

@Component
@Slf4j
public class RedisOperator {

  private static final String MODULE = "RedisOperator";

  private static final String LOCK_PREFIX = "file_datax:lock:";
  private static final String LUA_SCRIPT_SUCCESS = "OK";
  private static final Long LUA_DELETE_SUCCESS = 1L;

  private static final String RELEASE_LOCK_SCRIPT = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end
        """;

  private final MergeJedisCmd jedisCmd;

  public RedisOperator(MergeJedisCmd dataxRedis) {
    this.jedisCmd = dataxRedis;
  }

  public static SetParams getLockParams(int lockTimeout) {
    return SetParams.setParams()
        .nx()
        .ex(lockTimeout);
  }

  /**
   * 使用SetParams尝试获取分布式锁
   *
   * @param ea 企业账号
   * @param biz 业务标识
   * @param lockTimeout 锁超时时间（秒）
   * @return Optional.of(lockValue)获取成功，Optional.empty()获取失败
   */
  public Optional<String> tryLock(String ea, String biz, int lockTimeout) {
    String key = LOCK_PREFIX + biz + ":" + ea;
    String value = UUID.randomUUID().toString();
    try {
      String result = jedisCmd.set(key, value, getLockParams(lockTimeout));
      if (LUA_SCRIPT_SUCCESS.equals(result)) {
        return Optional.of(value);
      }
      return Optional.empty();
    } catch (Exception e) {
      throw new BaseException(e,MODULE+".tryLock", ErInfo.REDIS_TRY_LOCK_FAIL, ea, biz, e.getMessage());
    }
  }

  /**
   * 释放分布式锁
   *
   * @param ea 企业账号
   * @param biz 业务标识
   * @param lockValue 锁的值（用于验证锁的所有权）
   * @return Optional.of(true)释放成功，Optional.of(false)锁不存在或不属于当前客户端，Optional.empty()操作异常
   */
  public Optional<Boolean> releaseLock(String ea, String biz, String lockValue) {
    String key = LOCK_PREFIX + biz + ":" + ea;
    try {
      Object result = jedisCmd.eval(RELEASE_LOCK_SCRIPT, 1, key, lockValue);
      if (LUA_DELETE_SUCCESS.equals(result)) {
        return Optional.of(true);
      }
      return Optional.of(false);
    } catch (Exception e) {
      throw new BaseException(e, MODULE + ".releaseLock", ErInfo.REDIS_RELEASE_LOCK_FAIL, ea,
          biz, lockValue);
    }
  }

}


