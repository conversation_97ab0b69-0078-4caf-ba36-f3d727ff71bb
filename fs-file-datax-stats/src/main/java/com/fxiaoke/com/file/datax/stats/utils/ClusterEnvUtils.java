package com.fxiaoke.com.file.datax.stats.utils;

import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.k8s.support.util.SystemUtils.RuntimeEnv;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClusterEnvUtils {

  private static boolean firstShare = false;


  private ClusterEnvUtils() {
    throw new IllegalStateException("Utility class");
  }

  static {
    init(SystemUtils.getRuntimeEnv());
  }

  public static void init(RuntimeEnv runtimeEnv) {

    if (runtimeEnv == RuntimeEnv.FIRSTSHARE){
      firstShare = true;
    }

    log.info("clusterName:{},cluster description {}", runtimeEnv.name(), runtimeEnv.getName());
  }

  public static boolean firstShareRegisterDataStore() {
    return firstShare;
  }
}
