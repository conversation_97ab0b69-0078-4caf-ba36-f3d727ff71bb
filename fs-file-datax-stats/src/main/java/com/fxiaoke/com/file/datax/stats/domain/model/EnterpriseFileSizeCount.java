package com.fxiaoke.com.file.datax.stats.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class EnterpriseFileSizeCount {

  public static class Fields {
    public static final String EA = "ea";
    public static final String TOTAL_SIZE = "totalSize";
    public static final String COUNT = "count";
  }

  private String ea;
  private long totalSize;
  private long count;

  public EnterpriseFileSizeCount(long totalSize, long count) {
    this.totalSize = totalSize;
    this.count = count;
  }
}
