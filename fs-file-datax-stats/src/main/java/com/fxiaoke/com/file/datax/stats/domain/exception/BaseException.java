package com.fxiaoke.com.file.datax.stats.domain.exception;

import com.fxiaoke.com.file.datax.stats.domain.constants.Constant;
import com.fxiaoke.com.file.datax.stats.domain.constants.ErInfo;
import com.github.trace.aop.IgnorableException;
import java.util.Arrays;
import lombok.Getter;

@Getter
public class BaseException extends RuntimeException implements IgnorableException {
  private static final String ARGS_NAME = "args:";
  private static final String MODULE_NAME = "module:";
  private static final String ERROR_REASON = "reason:";
  private final int code;
  private final String message;
  private final String reason;

  public BaseException(String module, ErInfo info, Object... args) {
    super(info.getMessage());
    this.code = info.getCode();
    this.message = info.getMessage();
    this.reason = MODULE_NAME + module +" "+
        ERROR_REASON + info.getReason() +" "+
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(String module,String errMsg,Object... args){
    super(errMsg);
    this.code = 500;
    this.message = errMsg;
    this.reason = MODULE_NAME + module +" "+
        ERROR_REASON + errMsg +" "+
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(String module,int code,String errMsg,Object... args){
    super(errMsg);
    this.code = code;
    this.message = errMsg;
    this.reason = MODULE_NAME + module +" "+
        ERROR_REASON + errMsg +" "+
        ARGS_NAME + Arrays.toString(args);
  }

  public BaseException(Exception e,String module, ErInfo info,Object... args) {
    super(e.getMessage(), e);
    this.code = info.getCode();
    this.message = info.getMessage();
    this.reason = MODULE_NAME + module +" "+
        ERROR_REASON + info.getReason() +" "+
        ARGS_NAME + Arrays.toString(args);
  }

  @Override
  public String getErrorCode() {
    if (code<500) {
      return Constant.RPC_TRACE_IGNORE_CODE;
    }
    return code+"";
  }
}
