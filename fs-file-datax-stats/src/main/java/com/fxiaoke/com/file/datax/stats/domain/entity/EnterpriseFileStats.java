package com.fxiaoke.com.file.datax.stats.domain.entity;

import com.fxiaoke.com.file.datax.stats.domain.fieids.EnterpriseFileStatsFieIds;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@Entity(value = "EnterpriseFileStats", noClassnameStored = true)
@Indexes({
    @Index(fields = @Field(EnterpriseFileStatsFieIds.ea),
        options = @IndexOptions(name = EnterpriseFileStatsFieIds.ea_index_name, background = true))
})
public class EnterpriseFileStats {

  @Id
  private ObjectId id;

  /**
   * 企业账号
   */
  @Property(EnterpriseFileStatsFieIds.ea)
  private String ea;

  /**
   * 已使用文件存储配额，单位为字节
   */
  @Property(EnterpriseFileStatsFieIds.usedQuota)
  private long usedQuota;

  /**
   * 企业文件数量
   */
  @Property(EnterpriseFileStatsFieIds.fileCount)
  private long fileCount;

  /**
   * 企业文件存储配额，单位为字节(来源：license）
   */
  @Property(EnterpriseFileStatsFieIds.licenseQuota)
  private long licenseQuota;

  /**
   * 创建时间
   */
  @Property(EnterpriseFileStatsFieIds.createTime)
  private long createTime;

  /**
   * 最后更新时间
   */
  @Property(EnterpriseFileStatsFieIds.lastUpdateTime)
  private long lastUpdateTime;

  /**
   * 版本号
   */
  @Property(EnterpriseFileStatsFieIds.version)
  private int version = 1;

  /**
   * 预设值
   * 用于进行一些默认配置或初始化操作
   * 每当一项任务完成时,应重设此值为0
   */
  @Property(EnterpriseFileStatsFieIds.preset)
  private int preset = 0;
}
