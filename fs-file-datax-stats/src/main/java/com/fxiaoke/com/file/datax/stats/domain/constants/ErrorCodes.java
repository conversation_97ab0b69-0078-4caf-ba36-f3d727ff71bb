package com.fxiaoke.com.file.datax.stats.domain.constants;

/**
 * 错误码和错误消息常量定义
 * 统一管理系统中所有的错误码和错误消息
 */
public final class ErrorCodes {

    private ErrorCodes() {
        // 工具类不允许实例化
    }

    // ===== HTTP状态码 =====
    public static final int HTTP_OK = 200;
    public static final int HTTP_BAD_REQUEST = 400;
    public static final int HTTP_UNAUTHORIZED = 401;
    public static final int HTTP_FORBIDDEN = 403;
    public static final int HTTP_NOT_FOUND = 404;
    public static final int HTTP_METHOD_NOT_ALLOWED = 405;
    public static final int HTTP_REQUEST_TIMEOUT = 408;
    public static final int HTTP_PAYLOAD_TOO_LARGE = 413;
    public static final int HTTP_UNSUPPORTED_MEDIA_TYPE = 415;
    public static final int HTTP_TOO_MANY_REQUESTS = 429;
    public static final int HTTP_INTERNAL_SERVER_ERROR = 500;
    public static final int HTTP_BAD_GATEWAY = 502;
    public static final int HTTP_SERVICE_UNAVAILABLE = 503;
    public static final int HTTP_GATEWAY_TIMEOUT = 504;
    
    // 自定义状态码
    public static final int HTTP_CLIENT_CLOSED_REQUEST = 499; // nginx定义的客户端关闭连接状态码

    // ===== 通用错误消息 =====
    public static final String UNKNOWN_ERROR_MSG = "Unknown exception, please contact the admin";
    public static final String SYSTEM_BUSY_MSG = "System is busy, please try again later";
    public static final String OPERATION_FAILED_MSG = "Operation failed";
    public static final String ACCESS_DENIED_MSG = "Access denied";
    public static final String RESOURCE_NOT_FOUND_MSG = "Resource not found";

    // ===== 请求相关错误消息 =====
    public static final String INVALID_REQUEST_MSG = "Invalid request";
    public static final String REQUIRED_BODY_MISSING_MSG = "Required request body is missing";
    public static final String INVALID_JSON_FORMAT_MSG = "Invalid JSON format";
    public static final String ASYNC_TIMEOUT_MSG = "Async request timeout";
    public static final String REQUEST_TIMEOUT_MSG = "Request timeout";

    // ===== 参数验证错误消息 =====
    public static final String PARAMETER_VALIDATION_FAILED_MSG = "Parameter validation failed";
    public static final String REQUIRED_PARAMETER_MISSING_MSG = "Required parameter is missing";
    public static final String PARAMETER_TYPE_MISMATCH_MSG = "Parameter type mismatch";
    public static final String PARAMETER_FORMAT_ERROR_MSG = "Parameter format error";

    // ===== 文件相关错误消息 =====
    public static final String FILE_SIZE_EXCEEDED_MSG = "File size exceeds maximum allowed limit";
    public static final String FILE_TYPE_NOT_SUPPORTED_MSG = "File type not supported";
    public static final String FILE_UPLOAD_FAILED_MSG = "File upload failed";
    public static final String FILE_DOWNLOAD_FAILED_MSG = "File download failed";
    public static final String FILE_NOT_FOUND_MSG = "File not found";

    // ===== 业务相关错误消息 =====
    public static final String JOB_NOT_FOUND_MSG = "Job not found";
    public static final String JOB_PROCESSING_FAILED_MSG = "Job processing failed";
    public static final String DOCUMENT_PARSE_FAILED_MSG = "Document parsing failed";
    public static final String TOKEN_USAGE_EXCEEDED_MSG = "Token usage exceeded";
    public static final String QUOTA_EXCEEDED_MSG = "Quota exceeded";

    // ===== 网络相关错误消息 =====
    public static final String NETWORK_ERROR_MSG = "Network error";
    public static final String CONNECTION_TIMEOUT_MSG = "Connection timeout";
    public static final String SERVICE_UNAVAILABLE_MSG = "Service temporarily unavailable";
    public static final String EXTERNAL_SERVICE_ERROR_MSG = "External service error";

    // ===== 数据相关错误消息 =====
    public static final String DATA_NOT_FOUND_MSG = "Data not found";
    public static final String DATA_INTEGRITY_ERROR_MSG = "Data integrity error";
    public static final String DUPLICATE_DATA_MSG = "Duplicate data";
    public static final String DATA_PROCESSING_ERROR_MSG = "Data processing error";
} 