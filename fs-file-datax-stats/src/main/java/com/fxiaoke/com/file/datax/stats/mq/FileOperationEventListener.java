package com.fxiaoke.com.file.datax.stats.mq;

import com.fxiaoke.com.file.datax.stats.domain.constants.Operation;
import com.fxiaoke.com.file.datax.stats.domain.model.FileOpEvent;
import com.fxiaoke.com.file.datax.stats.domain.model.IncFileUsedQuotaItem;
import com.fxiaoke.com.file.datax.stats.service.StatisticalService;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * creator: liuys CreateTime: 2025-06-25 Description: 统一处理文件操作事件，包括上传和删除
 */
@Component
@Slf4j(topic = "FileOperationEventListener")
public class FileOperationEventListener implements MessageListenerConcurrently {
  private final Gson gson = new Gson();
  private final StatisticalService statisticService;
  public FileOperationEventListener(StatisticalService statisticService){
    this.statisticService = statisticService;
  }

  @Override
  public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs,
      ConsumeConcurrentlyContext context) {
    for (MessageExt msg : msgs) {
      try {
        String body = new String(msg.getBody());
        processFileOperationEvent(gson.fromJson(body, FileOpEvent.class));
      } catch (Exception e) {
        log.error("Failed to process file operation event, msgId: {}", msg.getMsgId(), e);
        // 考虑稍后重试
        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
      }
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
  }

  private void processFileOperationEvent(FileOpEvent event) {
    if (event.getEnterpriseAccount() == null || event.getSize() <= 0) {
      log.warn("Invalid delete event received, skipping. event:{}", event);
      return;
    }
    log.info("FileOp event: {}", event.toLog());
    if (event.getOperation() == Operation.UPLOAD) {
      processUpload(event);
    } else if (event.getOperation() == Operation.DELETE) {
      processDelete(event);
    }
  }


  private void processUpload(FileOpEvent event) {

    IncFileUsedQuotaItem item = new IncFileUsedQuotaItem();
    item.setEa(event.getEnterpriseAccount());
    item.setQuota(event.getSize());
    item.setCount(1);

    statisticService.incFileUsedQuota(item);
  }

  private void processDelete(FileOpEvent event) {

    IncFileUsedQuotaItem item = new IncFileUsedQuotaItem();
    item.setEa(event.getEnterpriseAccount());
    item.setQuota(-event.getSize());
    item.setCount(-1);

    statisticService.incFileUsedQuota(item);
  }
}