package com.fxiaoke.com.file.datax.stats.service;

import com.fxiaoke.com.file.datax.stats.domain.model.EnterpriseFileSizeCount;
import com.fxiaoke.com.file.datax.stats.domain.model.IncFileUsedQuotaItem;
import java.util.Date;
import java.util.List;

public interface StatisticalService {

  /**
   * 原子性累计已使用配额、文件数量
   *
   * @param item 待累计的配额项
   */
  void incFileUsedQuota(IncFileUsedQuotaItem item);

  /**
   * 批量原子性累计已使用配额、文件数量
   * 根据EA进行合并后更新
   * @param items 待累计的配额项列表
   */
  void incFileUsedQuotas(List<IncFileUsedQuotaItem> items);

  /**
   * 统计企业创建到指定时间范围的文件总大小与文件总数。
   *
   * @param ea 企业标识
   * @param endDate 结束时间
   * @return 企业文件大小统计数据
   */
  EnterpriseFileSizeCount statsFileHistoryUsedQuota(String ea, Date endDate);

  /**
   * 补充统计历史文件用量配额
   * 批量处理所有preset=0的企业记录，使用分布式锁避免重复处理
   * 支持并发处理和异常重试机制
   */
  void backfillHistoryStats();

  /**
   * 补充统计指定企业的历史文件用量配额
   * @param ea 企业标识
   * 支持并发处理和异常重试机制
   */
  void backfillHistoryStatsByEa(String ea);
}
