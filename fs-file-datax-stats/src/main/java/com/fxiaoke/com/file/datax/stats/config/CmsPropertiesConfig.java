package com.fxiaoke.com.file.datax.stats.config;

import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@Slf4j(topic = "CmsPropertiesConfig")
@ConfigurationProperties(prefix = "cms.file.datax.stats")
public class CmsPropertiesConfig {

  private String dataxRedisConfigName;

  // configName:fs-warehouse-enterprise-metadata-mongo
  private String warehouseMongoConfigName;

  // configName:fs-stone-metadata-mongo-n (仅测试环境使用)
  private String nMongoConfigName;

  // configName:fs-stone-metadata-mongo-n1
  private String n1MongoConfigName;
  // configName:fs-stone-metadata-mongo-n2
  private String n2MongoConfigName;

  // configName:fs-stone-shard-mongo
  private String shardMongoConfigName;

  // filesystem-rocketmq-consumer.ini
  private String mqConfigName;

  // common,name_server_08,file_datax_stats
  private String fileStatsMQSection;

  // 是否启用企业历史数据统计
  private boolean enableBackfillHistoryStats;

  // 是否允许运行历史数据统计
  private boolean runtimeBackfillHistoryStats;

  // 计算历史统计数据时的Redis锁超时时间,单位秒
  private int backfillLockTimeout;

  // 聚合查询企业时间分片内文件数量与文件总大小,单位分钟
  private int backfillHistoryStatsMinPart;

  // 等待聚合查询的数据库名称集合
  private List<String> backfillDbNames;

  // 聚合查询的时间分片的起始时间
  private Date backfillStartDate;

  // 聚合查询的时间分片的结束时间
  private  Date backfillEndDate;
}
