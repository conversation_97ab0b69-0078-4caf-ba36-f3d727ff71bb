package com.fxiaoke.com.file.datax.stats.dao.impl;

import com.fxiaoke.com.file.datax.stats.config.condition.ConditionalOnDataStore;
import com.fxiaoke.com.file.datax.stats.dao.StoneCoreMetaDao;
import com.fxiaoke.com.file.datax.stats.domain.constants.DataBaseCluster;
import com.fxiaoke.com.file.datax.stats.domain.constants.ErInfo;
import com.fxiaoke.com.file.datax.stats.domain.exception.BaseException;
import com.fxiaoke.com.file.datax.stats.domain.model.StoneCoreConfig;
import com.fxiaoke.com.file.datax.stats.service.impl.AsmServiceImpl;
import com.github.mongo.support.DatastoreExt;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@ConditionalOnDataStore()
public class N1N2DataStoreDataBaseManager extends CommonDataBaseManager{

  protected static final String MODULE = "N1N2DataStoreDataBaseManager";

  @Resource
  private DatastoreExt n1DataStore;

  @Resource
  private DatastoreExt n2DataStore;

  @Resource
  private DatastoreExt shardDataStore;

  public N1N2DataStoreDataBaseManager(
      AsmServiceImpl asmServiceImpl,
      StoneCoreMetaDao stoneCoreMetaDao) {
    super(asmServiceImpl, stoneCoreMetaDao);
  }

  // 设置优先读从库
  public DatastoreExt getReadDataStore(String ea) {
    return getEnterpriseReadDataStore(ea);
  }

  private DatastoreExt getEnterpriseReadDataStore(String ea) {
    StoneCoreConfig stoneCoreConfig = loadStoneCoreConfig(ea);
    DataBaseCluster cluster = stoneCoreConfig.getCluster();
    return switch (cluster) {
      case N1 -> n1DataStore.use(stoneCoreConfig.getDbName());
      case N2 -> n2DataStore.use(stoneCoreConfig.getDbName());
      case Shard -> shardDataStore.setTenantId(stoneCoreConfig.getTenantId());
      default ->
        // 如果不是N集群或分片集群，则抛出异常
          throw new BaseException(MODULE, ErInfo.DATABASE_NO_ROUTE, ea);
    };
  }

}
