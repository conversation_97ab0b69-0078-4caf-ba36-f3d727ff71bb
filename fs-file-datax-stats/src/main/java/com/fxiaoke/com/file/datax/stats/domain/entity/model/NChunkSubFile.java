package com.fxiaoke.com.file.datax.stats.domain.entity.model;

import com.fxiaoke.com.file.datax.stats.domain.fieids.NFileMetaFieIds;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class NChunkSubFile {

  @Property(NFileMetaFieIds.nChunkSubFiles_nChunkSubFiles_index)
  private int index;

  @Property(NFileMetaFieIds.nChunkSubFiles_nChunkSubFiles_group)
  private String group;

  @Property(NFileMetaFieIds.nChunkSubFiles_nChunkSubFiles_masterId)
  private String masterId;

  @Property(NFileMetaFieIds.nChunkSubFiles_nChunkSubFiles_size)
  private int size;

  @Property(NFileMetaFieIds.nChunkSubFiles_nChunkSubFiles_createDate)
  private Date createDate;
}
