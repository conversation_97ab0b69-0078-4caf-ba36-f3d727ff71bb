package com.fxiaoke.com.file.datax.stats.config.condition;

import com.fxiaoke.com.file.datax.stats.utils.ClusterEnvUtils;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class DataStoreRegisterCondition implements Condition {

  @Override
  public boolean matches(@NotNull ConditionContext context,
      @NotNull AnnotatedTypeMetadata metadata) {

    Map<String, Object> attributes = metadata.getAnnotationAttributes(
        ConditionalOnDataStore.class.getName());

    if (attributes == null) {
      throw new RuntimeException(
          "ConditionalOnDataStore annotation is not present on the class or method.");
    }

    Boolean firstShareDataStore = (Boolean) attributes.get("firstShareDataStore");

    return Boolean.TRUE.equals(firstShareDataStore) == ClusterEnvUtils.firstShareRegisterDataStore();
  }
}
