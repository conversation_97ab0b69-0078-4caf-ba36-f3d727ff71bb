package com.fxiaoke.com.file.datax.stats.utils;

import com.fxiaoke.com.file.datax.stats.domain.model.MongoObjectIdTimeSlice;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

public class DBUtil {

  private DBUtil() {
  }

  private static final int MAX_MIN_PART = 1440 * 7; // 最大分片间隔为7天（1440分钟 * 7天）

  /**
   * 获取指定时间范围内的时间分片列表,用与按时间片查询MongoDB
   * 注：考虑到Date转换为ObjectId（秒级别）时间的精度损失,因此会对startDate前推1秒,endDate后推1秒,补偿精度损失
   *
   * @param startDate 起始时间
   * @param endDate   结束时间
   * @param minPart   最小分片间隔（分钟）
   * @return 时间分片列表
   */
  public static List<MongoObjectIdTimeSlice> getSecTimeSlice(Date startDate, Date endDate,
      int minPart) {
    List<MongoObjectIdTimeSlice> result = new ArrayList<>();

    // 参数校验
    if (startDate == null || endDate == null || minPart <= 0 || minPart > MAX_MIN_PART) {
      return result;
    }
    if (startDate.after(endDate)) {
      return result;
    }

    // 对输入的起止时间进行精度补偿
    Date compensatedStartDate = new Date(startDate.getTime() - 1000); // 起始时间减1秒
    Date compensatedEndDate = new Date(endDate.getTime() + 1000);     // 结束时间加1秒

    // 计算时间间隔（毫秒）
    long intervalMillis = minPart * 60 * 1000L;

    // 对齐开始时间到秒
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(compensatedStartDate);
    calendar.set(Calendar.MILLISECOND, 0);

    Date sliceStart = calendar.getTime();
    while (sliceStart.before(compensatedEndDate)) {
      // 计算分片结束时间
      calendar.setTime(sliceStart);
      calendar.add(Calendar.MILLISECOND, (int) intervalMillis);
      Date sliceEnd = calendar.getTime();

      // 如果分片结束时间超过补偿后的结束时间，则使用补偿后的结束时间
      if (sliceEnd.after(compensatedEndDate)) {
        sliceEnd = compensatedEndDate;
      }

      // 创建ObjectId时间分片
      ObjectId startObjectId = new ObjectId(sliceStart);
      ObjectId endObjectId = new ObjectId(sliceEnd);

      result.add(new MongoObjectIdTimeSlice(startObjectId, endObjectId));

      // 移动到下一个分片开始时间
      sliceStart = sliceEnd;
    }

    return result;
  }
}
