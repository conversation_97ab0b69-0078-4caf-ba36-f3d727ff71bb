package com.fxiaoke.com.file.datax.stats.dao.impl;

import com.fxiaoke.com.file.datax.stats.dao.StoneCoreMetaDao;
import com.fxiaoke.com.file.datax.stats.domain.entity.StoneCoreMeta;
import com.fxiaoke.com.file.datax.stats.domain.fieids.StoneCoreMetaFieIds;
import com.github.mongo.support.DatastoreExt;
import java.util.Optional;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class StoneCoreMetaDaoImpl implements StoneCoreMetaDao {

  private final DatastoreExt warehouseDataStore;

  public StoneCoreMetaDaoImpl(DatastoreExt warehouseDataStore) {
    this.warehouseDataStore = warehouseDataStore;
  }

  private Query<StoneCoreMeta> getQuery() {
    return warehouseDataStore.createQuery(StoneCoreMeta.class);
  }

  @Override
  public Optional<StoneCoreMeta> find(String ea) {
    Query<StoneCoreMeta> query = getQuery();
    query.filter(StoneCoreMetaFieIds.ea, ea);
    return Optional.ofNullable(query.get());
  }

}
