package com.fxiaoke.com.file.datax.stats.utils;

import com.fxiaoke.com.file.datax.stats.domain.constants.PathType;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 文件路径处理工具类 支持处理不同类型的文件系统ID，包括： - N_: 普通文件 - TN_: 临时普通文件 - C_: 缓存文件 - TC_: 临时缓存文件
 */
public class PathStrUtil {

  // 路径前缀常量
  private static final String N_FILE_PREFIX = "N_";
  private static final String TN_FILE_PREFIX = "TN_";
  private static final String C_FILE_PREFIX = "C_";
  private static final String TC_FILE_PREFIX = "TC_";
  private static final String A_FILE_PREFIX = "A_";
  private static final String TA_FILE_PREFIX = "TA_";
  private static final String G_FILE_PREFIX = "G_";
  private static final String OLD_FILE_PREFIX = "20_"; // 老文件前缀
  private static final String S_FILE_PREFIX = "S_";
  private static final String F_FILE_PREFIX = "F_";
  private static final String BIG_FILE_PREFIX = "ALIOSS"; // 大附件对象存储前缀

  private static final String FILE_THUMB_SUFFIX = ".thumb";

  // 特殊后缀常量
  private static final String ASYNC_THUMB_FILE_SUFFIX = "_tmb";
  private static final String ASYNC_THUMB_CHUNK_FILE_SUFFIX = "_tmb_chunk";

  // ID长度常量
  private static final int TEMP_ID_LENGTH = 35; // TN_ + 32位UUID = 35
  private static final int THUMB_TAG_LENGTH = FILE_THUMB_SUFFIX.length();

  // 正则表达式模式
  private static final Pattern ASYNC_THUMB_PATTERN = Pattern.compile("TN_(.{32})_tmb\\.(.+)");
  private static final Pattern THUMBNAIL_PATH_RULE = Pattern.compile(
      "[0-9a-f]{32}([2-9]|.thumb.\\d)+$");
  private static final Pattern THUMBNAIL_PATH_RULE_29 = Pattern.compile(
      "[0-9a-f]{29}(.thumb.\\d)+$");
  private static final Pattern THUMBNAIL_PATH_RULE_30 = Pattern.compile(
      "[0-9a-f]{30}(.thumb.\\d)+$");
  private static final Pattern THUMBNAIL_PATH_RULE_31 = Pattern.compile(
      "[0-9a-f]{31}(.thumb.\\d)+$");

  /**
   * 获取路径类型
   *
   * @param path 待分析的路径
   * @return 路径类型枚举
   */
  public static PathType getPathType(String path) {

    if (path == null || path.isEmpty()) {
      return PathType.UNKNOWN;
    }

    if (isNFilePath(path)) {
      return PathType.N_FILE;
    } else if (isTNFilePath(path)) {
      return PathType.TN_FILE;
    } else if (isCFilePath(path)) {
      return PathType.C_FILE;
    } else if (isTCFilePath(path)) {
      return PathType.TC_FILE;
    } else if (isAFilePath(path)) {
      return PathType.A_FILE;
    } else if (isTAFilePath(path)) {
      return PathType.TA_FILE;
    } else if (isGFilePath(path)) {
      return PathType.G_FILE;
    } else if (isOldFilePath(path)) {
      return PathType.OLD_FILE;
    } else if (isSFilePath(path)) {
      return PathType.S_FILE;
    } else if (isFFilePath(path)) {
      return PathType.F_FILE;
    } else if (isBigFilePath(path)) {
      return PathType.BIG_FILE;
    }

    return PathType.UNKNOWN;
  }

  public static boolean isEnterpriseFile(PathType pathType){
    return pathType == PathType.N_FILE || pathType == PathType.TN_FILE || pathType == PathType.C_FILE
        || pathType == PathType.TC_FILE;
  }

  public static String getBasePath(PathType pathType, String path) {
    return switch (pathType) {
      case N_FILE -> getNFileBasePath(path);
      case TN_FILE -> getTNFileBasePath(path);
      case C_FILE -> getCFileBasePath(path);
      case TC_FILE -> getTCFileBasePath(path);
      case A_FILE -> getAFileBasePath(path);
      case TA_FILE -> getTAFileBasePath(path);
      case G_FILE -> getGFileBasePath(path);
      case OLD_FILE -> getOldFileBasePath(path);
      case S_FILE -> getSFileBasePath(path);
      case F_FILE -> getFFileBasePath(path);
      case BIG_FILE -> getBigFileBasePath(path);
      default -> path;
    };
  }

  /**
   * 判断是否为当天创建时间的企业文件
   * @param pathType 路径类型
   * @param path 待判断的路径
   * @return true表示是当前时间创建的企业文件，false表示不是
   */
  public static boolean isCurrentTimeEnterpriseFile(PathType pathType,String path) {

    boolean enterpriseFile = isEnterpriseFile(pathType);
    if (!enterpriseFile) {
      return false;
    }

    String basePath = getBasePath(pathType, path);

    String[] pathList = basePath.split("_");

    String pathCreateTime = pathList[1] + pathList[2];

    String currentTime = getCurrentDate("yyyyMMdd");

    return currentTime.equals(pathCreateTime);
  }

  public static String getCurrentDate(String format) {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
    return currentDate.format(formatter);
  }

  public static boolean is44NPath(String path, PathType pathType) {
    if (path == null || path.isEmpty()) {
      return false;
    }
    if (pathType != PathType.N_FILE) {
      return false;
    }
    return path.length() == 44;
  }

  private static long countChar(String str, char targetChar) {
    return str.chars().filter(ch -> ch == targetChar).count();
  }

  private static String getSuffixFreePath(String path) {
    int dotIndex = path.indexOf('.');
    return dotIndex == -1 ? path : path.substring(0, dotIndex);
  }

  /**
   * 判断是否为N文件路径
   *
   * @param path 待判断的路径 如：<br>
   * 1. N_202409_30_92d3a5c3c67c44bb89ac108ee951eddd <br>
   * 2. N_202501_14_1bc8a8abc70f4e6d89370276b0b22a80.thumb.1 <br>
   * 3. N_202505_17_72320ed1aaf94a939daa99229c382751.thumb.4 <br>
   * @return true表示是N文件路径，false表示不是
   */
  private static boolean isNFilePath(String path) {

    if (!path.startsWith(N_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 3) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() >= 44 && suffixFreePath.length() <= 46;
  }

  private static String getNFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为TN文件
   *
   * @param path 待判断的路径 如：<br>
   * 1. TN_9180530adf6343f889b72efa3f7a9827 <br>
   * 2. TN_5ad8e67507c44b39854dded33dc2116e <br>
   * 3. TN_b66124bb7e9a4ed1a755cc50220944cd_tmb <br>
   * 4. TN_cb2fb3065a41484db1d251ce1471b812_tmb_chunk <br>
   * @return true表示是TN文件，false表示不是
   */
  private static boolean isTNFilePath(String path) {

    if (!path.startsWith(TN_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    String suffixFreePath = getSuffixFreePath(path);

    if (underlineCount == 1) {
      return suffixFreePath.length() == 35;
    }

    if (underlineCount == 2) {
      return suffixFreePath.length() == 39;
    }

    if (underlineCount == 3) {
      return suffixFreePath.length() == 45;
    }

    return false;
  }

  private static String getTNFileBasePath(String path){
    long underlineCount = countChar(path, '_');

    String suffixFreePath = getSuffixFreePath(path);

    if (underlineCount == 2 || underlineCount == 3) {
      return suffixFreePath.substring(0, 35);
    }

    return suffixFreePath;
  }

  /**
   * 判断是否为C文件
   *
   * @param path 待判断的路径 如：<br>
   * 1. C_202410_10_4c6dba9edcda4e4faf4609ac8528745e <br>
   * @return true表示是N文件，false表示不是
   */
  private static boolean isCFilePath(String path) {

    if (!path.startsWith(C_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 3) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() == 44;
  }

  private static String getCFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为TC文件
   *
   * @param path 待判断的路径 如：<br>
   * 1. TC_7bab2d4b70874fb592120e150e8e7452 <br>
   * @return true表示是TC文件，false表示不是
   */
  private static boolean isTCFilePath(String path) {

    if (!path.startsWith(TC_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 1) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() == 35;
  }

  private static String getTCFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为A文件路径
   *
   * @param path 待判断的路径 如：<br>
   * 1. A_202303_23_71418fd566bb45408d5b1cd40c7712ba <br>
   * 2. A_201906_25_3ad905ea111e4bbbb45c7fbaf3c84655 <br>
   * @return true表示是A文件，false表示不是
   */
  private static boolean isAFilePath(String path) {

    if (!path.startsWith(A_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 3) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() == 44;
  }

  private static String getAFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为TA文件
   *
   * @param path 待判断的路径 如：<br>
   * 1. TA_ef9ccd029c4e4b20bc6ee92d409b1874 <br>
   * @return true表示是TA文件，false表示不是
   */
  private static boolean isTAFilePath(String path) {

    if (!path.startsWith(TA_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 1) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() == 35;
  }

  private static String getTAFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为G文件
   *
   * @param path 待判断的路径 如：<br>
   * 1. G_201607_13_fac69f795bc049138f970fe85666f0c1 <br>
   * 2. G_202409_30_92d3a5c3c67c44bb89ac108ee951eddd <br>
   * @return true表示是G文件，false表示不是
   */
  private static boolean isGFilePath(String path) {

    if (!path.startsWith(G_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 3) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() == 44;
  }

  private static String getGFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为老文件path
   *
   * @param path 待判断的路径 如：<br>
   * 1. 201504_30_2db390f0-cdc0-450a-8c71-a6c29426d2f14.jpg <br>
   * 2. 201505_27_15c20d14-9663-459d-ac0b-9ccea3256c5a3.jpg <br>
   * 3. 201505_05_e692ad86-07ec-45e2-b7d1-d10ce6d71df14.jpg <br>
   * 4. 201307_09_b21d7244-8abe-44d7-860b-27a76cc8efb4.amr <br>
   * 5. 201408_28_8ad24bcd-5323-49b0-bfc0-8b0d49499cab.docx <br>
   * 补充：移除扩展名后 图片类型47位路径、其他类型46位路径
   * @return true表示是老文件路径，false表示不是
   */
  private static boolean isOldFilePath(String path) {

    if (!path.startsWith(OLD_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 2) {
      return false;
    }

    long middleLineCount = countChar(path, '-');

    if (middleLineCount != 4) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() >= 46 && suffixFreePath.length() <= 47;
  }

  private static String getOldFileBasePath(String path) {
    return path;
  }

  /**
   * 判断是否为S文件路径
   *
   * @param path 待判断的路径 如：<br>
   * 1. S_201505_04_5588e1e60cae46f5a74cfc354aaa00dc <br>
   * 2. S_201505_04_807b549c473943d5ba8e8a779ab8cc62 <br>
   * @return true表示是S文件路径，false表示不是
   */
  private static boolean isSFilePath(String path) {

    if (!path.startsWith(S_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');

    if (underlineCount != 3) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);

    return suffixFreePath.length() == 44;
  }

  private static String getSFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为F文件路径
   *
   * @param path 待判断的路径 如：<br>
   * 1. F_ythcln_202409_02_5e5e329cc6f749ac8e5e582a3643016f <br>
   * @return true表示是F文件路径，false表示不是
   */
  private static boolean isFFilePath(String path) {

    if (!path.startsWith(F_FILE_PREFIX)) {
      return false;
    }

    long underlineCount = countChar(path, '_');
    
    if (underlineCount!=4){
      return false;
    }
    
    String suffixFreePath = getSuffixFreePath(path);

    // F文件路径格式为 F_ + 企业EA + 6位年月 + 2位日期 + 32位UUID 
    // 例如：F_ythcln_202409_02_5e5e329cc6f749ac8e5e582a3643016f
    // 其中EA部分长度为不确定，年月部分长度为6位，日期部分长度为2位，UUID部分长度为32位
    // 因此应校验 F+ 年月部分 + 日期部分 + UID部分 的长度

    String[] pathList = suffixFreePath.split("_");

    String infixFreePath = pathList[0] + pathList[2] + pathList[3] + pathList[4];

    return infixFreePath.length() == 41;
  }

  private static String getFFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 判断是否为大附件对象存储路径
   *
   * @param path 待判断的路径 如：<br>
   * 1. ALIOSS_69178641c8264ce585e6a21fef3601cf <br>
   * @return true表示是大附件对象存储路径，false表示不是
   */
  private static boolean isBigFilePath(String path) {

    // 大附件对象存储路径格式为 ALIOSS_ + 32位UUID
    if (!path.startsWith(BIG_FILE_PREFIX)) {
      return false;
    }

    String suffixFreePath = getSuffixFreePath(path);
    long underlineCount = countChar(suffixFreePath, '_');

    return underlineCount == 39;
  }

  private static String getBigFileBasePath(String path) {
    return getSuffixFreePath(path);
  }

  /**
   * 验证path是否是无效的企业文件Path 仅支持N_|TN_|C_|TC_|S_类型的文件Path
   *
   * @param path 待验证的路径
   * @return true表示路径无效，false表示有效
   */
  private static boolean invalidEnterprisePath(String path) {

    if (path == null || path.isEmpty()) {
      return true;
    }

    return !path.startsWith(N_FILE_PREFIX) && !path.startsWith(TN_FILE_PREFIX) && !path.startsWith(
        C_FILE_PREFIX) && !path.startsWith(TC_FILE_PREFIX);
  }

  /**
   * 判断是否为临时文件
   *
   * @param path 待判断的路径
   * @return true表示是临时文件，false表示不是
   */
  private static boolean isTempFile(String path) {
    if (path == null || path.isEmpty()) {
      return false;
    }
    return path.startsWith(TN_FILE_PREFIX) || path.startsWith(TC_FILE_PREFIX);
  }

  /**
   * 判断是否为缩略图文件
   *
   * @param path 待判断的路径
   * @return true表示是缩略图文件，false表示不是
   */
  private static boolean isThumbnailFile(String path) {
    if (path == null || path.isEmpty()) {
      return false;
    }
    return path.contains(ASYNC_THUMB_FILE_SUFFIX) || path.contains(FILE_THUMB_SUFFIX);
  }

  /**
   * 使用正则表达式判断是否为缩略图
   *
   * @param path 待判断的路径
   * @return true表示是缩略图，false表示不是
   */
  private static boolean isThumbnailByPattern(String path) {
    String basePath = getBasePath(path);
    return basePath != null && !basePath.isEmpty() && (THUMBNAIL_PATH_RULE.matcher(basePath).find()
        || THUMBNAIL_PATH_RULE_31.matcher(basePath).find() || THUMBNAIL_PATH_RULE_30.matcher(
        basePath).find() || THUMBNAIL_PATH_RULE_29.matcher(basePath).find());
  }

  /**
   * 判断是否为分块文件
   *
   * @param path 待判断的路径
   * @return true表示是分块文件，false表示不是
   */
  private static boolean isChunkFile(String path) {
    if (path == null || path.isEmpty()) {
      return false;
    }
    return path.contains(ASYNC_THUMB_CHUNK_FILE_SUFFIX);
  }

  /**
   * 判断是否为CDN路径
   *
   * @param path 待判断的路径
   * @return true表示是CDN路径，false表示不是
   */
  private static boolean isCdnPath(String path) {
    return path != null && !path.isEmpty() && (path.startsWith(C_FILE_PREFIX) || path.startsWith(
        TC_FILE_PREFIX));
  }

  /**
   * 获取原图路径（44位或44位+1两种Path）
   *
   * @param path 输入路径
   * @return 原图路径
   */
  private static String getOriginImagePath(String path) {
    if (path == null || path.isEmpty()) {
      return path;
    }

    // 兼容老文件格式
    if (countUnderscores(path) == 2 || path.startsWith(S_FILE_PREFIX)) {
      return path;
    }

    String processResult = getBasePath(path);
    if (path.contains(FILE_THUMB_SUFFIX)) {
      int dotIndex = path.indexOf('.');
      return dotIndex == -1 ? path : path.substring(0, dotIndex);
    }

    if (processResult.length() == 45) {
      return processResult.substring(0, 44) + '1';
    }
    if (processResult.length() > 45) {
      return processResult.substring(0, 44);
    }

    return path;
  }

  /**
   * 将不规范的文件系统id转换为规范的id 注：文件系统id用path做替代表示，使用不同的前缀来区分不同类型的文件。
   *
   * @param path 待处理的文件系统id
   * @return 处理后的basePath
   */
  private static String getBasePath(String path) {
    if (path == null || path.isEmpty()) {
      return path;
    }

    // 兼容老文件格式
    if (countUnderscores(path) == 2 || path.startsWith(S_FILE_PREFIX)) {
      return path;
    }

    int thumbTagIndex = path.indexOf(FILE_THUMB_SUFFIX);
    int thumbTagLength = thumbTagIndex + THUMB_TAG_LENGTH;

    // 如果没有.thumb标记，或者.thumb后面没有内容，或者.thumb后面不是点号
    if (thumbTagIndex == -1 || thumbTagLength == path.length() || (thumbTagLength < path.length()
        && path.charAt(thumbTagLength) != '.')) {
      // 处理TN_和TC_类型的路径
      if (path.startsWith(TN_FILE_PREFIX) || path.startsWith(TC_FILE_PREFIX)) {
        if (path.length() >= TEMP_ID_LENGTH) {
          return path.substring(0, TEMP_ID_LENGTH);
        }
        if (path.contains(ASYNC_THUMB_FILE_SUFFIX)) {
          int tmbIndex = path.indexOf(ASYNC_THUMB_FILE_SUFFIX);
          return path.substring(0, tmbIndex);
        }
      }

      int endIndex = path.indexOf('.');
      return endIndex == -1 ? path : path.substring(0, endIndex);
    }

    // 处理.thumb.X格式
    if (thumbTagLength + 1 < path.length()) {
      char offset1Char = path.charAt(thumbTagLength + 1);
      if (offset1Char >= '0' && offset1Char < '9') {
        return path.substring(0, thumbTagLength + 2);
      } else if (offset1Char == '9' && (thumbTagLength + 2) < path.length()
          && path.charAt(thumbTagLength + 2) == '9') {
        return path.substring(0, thumbTagLength + 3);
      }
    }

    int dotIndex = path.indexOf('.');
    return dotIndex == -1 ? path : path.substring(0, dotIndex);
  }

  /**
   * 获取缩略图索引
   *
   * @param path 缩略图路径
   * @return 缩略图索引
   */
  private static int getThumbIndex(String path) {
    if (path == null || path.isEmpty()) {
      return -1;
    }

    try {
      int lastDotIndex = path.lastIndexOf('.');
      if (lastDotIndex > 0 && lastDotIndex < path.length() - 1) {
        return Integer.parseInt(path.substring(lastDotIndex + 1));
      }
    } catch (NumberFormatException e) {
      // ignore
    }
    return -1;
  }

  /**
   * 获取缩略图基础名称
   *
   * @param path 缩略图路径
   * @return 基础名称
   */
  private static String getThumbBaseName(String path) {
    if (path == null || path.isEmpty()) {
      return path;
    }

    int dotIndex = path.indexOf('.');
    return dotIndex == -1 ? path : path.substring(0, dotIndex);
  }

  /**
   * 根据异步缩略图路径获取原始临时文件路径
   *
   * @param asyncPath 异步缩略图路径
   * @return 原始临时文件路径
   */
  private static String getBasePathWithoutAsyncTag(String asyncPath) {
    if (asyncPath == null || asyncPath.isEmpty()) {
      return asyncPath;
    }

    String basePath = getBasePath(asyncPath);
    if (ASYNC_THUMB_PATTERN.matcher(basePath).find()) {
      return basePath.substring(0, 35);
    }

    return basePath.endsWith(ASYNC_THUMB_FILE_SUFFIX) ? basePath.replace(ASYNC_THUMB_FILE_SUFFIX,
        "") : basePath;
  }

  /**
   * 从路径中提取文件ID（去除前缀后的部分）
   *
   * @param path 文件路径
   * @return 文件ID，如果路径无效则返回null
   */
  private static String getFileId(String path) {
    if (invalidEnterprisePath(path)) {
      return null;
    }

    String basePath = getBasePath(path);
    if (basePath == null) {
      return null;
    }

    PathType type = getPathType(basePath);
    if (type == PathType.UNKNOWN) {
      return null;
    }

    return basePath.substring(type.getPrefix().length());
  }

  /**
   * 构建标准路径
   *
   * @param pathType 路径类型
   * @param fileId   文件ID
   * @return 构建的标准路径
   */
  private static String buildPath(PathType pathType, String fileId) {
    if (pathType == null || pathType == PathType.UNKNOWN || fileId == null || fileId.isEmpty()) {
      return null;
    }

    return pathType.getPrefix() + fileId;
  }

  /**
   * 创建新的路径ID
   *
   * @param pathType 路径类型
   * @return 新创建的路径
   */
  private static String createPath(PathType pathType) {
    if (pathType == null || pathType == PathType.UNKNOWN) {
      return null;
    }

    String pathPrefix = pathType.getPrefix();

    // 临时文件使用UUID
    if (pathPrefix.startsWith("T")) {
      return pathPrefix + UUID.randomUUID().toString().replaceAll("-", "");
    }

    // 普通文件使用日期+UUID格式
    LocalDate localDate = LocalDate.now();
    return pathPrefix.substring(0, pathPrefix.lastIndexOf('_')) + "_" + localDate.format(
        DateTimeFormatter.ofPattern("yyyyMM")) + "_" + localDate.format(
        DateTimeFormatter.ofPattern("dd")) + "_" + UUID.randomUUID().toString().replaceAll("-", "");
  }

  /**
   * 构建缩略图路径
   *
   * @param basePath 基础路径
   * @return 缩略图路径
   */
  private static String buildThumbnailPath(String basePath) {
    if (invalidEnterprisePath(basePath)) {
      return null;
    }
    return basePath + ASYNC_THUMB_FILE_SUFFIX;
  }

  /**
   * 构建分块文件路径
   *
   * @param basePath   基础路径
   * @param chunkIndex 分块索引
   * @return 分块文件路径
   */
  private static String buildChunkPath(String basePath, int chunkIndex) {
    if (invalidEnterprisePath(basePath)) {
      return null;
    }
    return basePath + ASYNC_THUMB_CHUNK_FILE_SUFFIX + "_" + chunkIndex;
  }

  /**
   * 给路径添加扩展名
   *
   * @param path 路径
   * @param ext  扩展名
   * @return 带扩展名的路径
   */
  private static String getPathWithExt(String path, String ext) {
    if (ext == null || ext.isEmpty()) {
      return path;
    }
    return path + "." + ext;
  }

  /**
   * 获取基础C路径（CDN路径处理）
   *
   * @param path CDN路径
   * @return 基础C路径
   */
  private static String getBaseCPath(String path) {
    if (path == null || path.isEmpty()) {
      return path;
    }

    if (path.length() >= 45) {
      return path.substring(0, 44);
    }
    return path;
  }

  /**
   * 从路径中提取文件扩展名
   *
   * @param path 文件路径
   * @return 文件扩展名（不包含点号），如果没有扩展名则返回null
   */
  private static String getFileExtension(String path) {
    if (path == null || path.isEmpty()) {
      return null;
    }

    int lastDotIndex = path.lastIndexOf('.');
    if (lastDotIndex > 0 && lastDotIndex < path.length() - 1) {
      return path.substring(lastDotIndex + 1);
    }

    return null;
  }

  /**
   * 验证路径格式是否正确
   *
   * @param path 待验证的路径
   * @return 验证结果描述，如果格式正确返回null
   */
  private static String validatePathFormat(String path) {
    if (path == null) {
      return "Path cannot be null";
    }

    if (path.isEmpty()) {
      return "Path cannot be empty";
    }

    if (invalidEnterprisePath(path)) {
      return "Path must start with N_, TN_, C_, TC_, or S_";
    }

    PathType type = getPathType(path);
    String fileId = getFileId(path);

    if (fileId == null || fileId.isEmpty()) {
      return "Invalid file ID in path";
    }

    // 验证临时文件的ID长度
    if ((type == PathType.TN_FILE || type == PathType.TC_FILE) && !path.contains(".")
        && !path.contains("_tmb") && path.length() != TEMP_ID_LENGTH) {
      return "Temporary file path should have " + TEMP_ID_LENGTH + " characters for base path";
    }

    return null; // 格式正确
  }

  /**
   * 计算字符串中指定字符的数量
   *
   * @param str 输入字符串
   * @return 下划线数量
   */
  private static int countUnderscores(String str) {
    if (str == null || str.isEmpty()) {
      return 0;
    }

    int count = 0;
    for (char c : str.toCharArray()) {
      if (c == '_') {
        count++;
      }
    }
    return count;
  }
}
