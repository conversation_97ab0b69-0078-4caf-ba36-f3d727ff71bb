package com.fxiaoke.com.file.datax.stats.dao.impl;

import com.fxiaoke.com.file.datax.stats.dao.NFileMetaDao;
import com.fxiaoke.com.file.datax.stats.domain.constants.PathType;
import com.fxiaoke.com.file.datax.stats.domain.entity.NFileMeta;
import com.fxiaoke.com.file.datax.stats.domain.fieids.NFileMetaFieIds;
import com.fxiaoke.com.file.datax.stats.domain.model.EnterpriseFileSizeCount;
import com.fxiaoke.com.file.datax.stats.domain.model.MongoObjectIdTimeSlice;
import com.fxiaoke.com.file.datax.stats.utils.PathStrUtil;
import com.mongodb.ReadPreference;
import java.util.Iterator;
import java.util.Optional;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.aggregation.Projection;
import org.mongodb.morphia.aggregation.Accumulator;
import org.mongodb.morphia.aggregation.AggregationPipeline;
import org.mongodb.morphia.aggregation.Group;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class NFileMetaDaoImpl implements NFileMetaDao {

  // 根据Path 的日期是否与当前年月日一致决定使用主库查询还是从库查询
  private Query<NFileMeta> getQuery(Datastore db, PathType pathType, String path) {
    boolean currentTimeEnterpriseFile = PathStrUtil.isCurrentTimeEnterpriseFile(pathType, path);
    // 是当天创建的企业文件查询主库否则查询从库
    if (currentTimeEnterpriseFile) {
      return db.createQuery(NFileMeta.class).useReadPreference(ReadPreference.primary());
    } else {
      return db.createQuery(NFileMeta.class).useReadPreference(ReadPreference.secondaryPreferred());
    }
  }

  @Override
  public Optional<NFileMeta> find(Datastore db, PathType pathType, String ea, String path) {

    Query<NFileMeta> query = getQuery(db, pathType, path);
    query.field(NFileMetaFieIds.ea).equal(ea);

    switch (pathType) {
      case N_FILE -> {
        if (PathStrUtil.is44NPath(path, pathType)) {
          query.or(
              query.criteria(NFileMetaFieIds.nPath).equal(path),
              query.criteria(NFileMetaFieIds.nPath).equal(path + "1")
          );
        } else {
          query.field(NFileMetaFieIds.nPath).equal(path);
        }
      }
      case TN_FILE -> query.field(NFileMetaFieIds.tnPath).equal(path);
      case C_FILE -> query.field(NFileMetaFieIds.cPath).equal(path);
      case TC_FILE -> query.or(query.criteria(NFileMetaFieIds.cPath).equal(path),
          query.criteria(NFileMetaFieIds.tcPath).equal(path));
    }

    return Optional.ofNullable(query.get());
  }

  private Query<NFileMeta> getSliceQuery(Datastore db,
      MongoObjectIdTimeSlice slice) {
    // 创建时间分片查询，优先使用从库读取
    return db.createQuery(NFileMeta.class)
        .useReadPreference(ReadPreference.secondaryPreferred())
        .field(NFileMetaFieIds.id).greaterThan(slice.start())
        .field(NFileMetaFieIds.id).lessThan(slice.end())
        .retrievedFields(true,NFileMetaFieIds.size);
  }

  private Query<NFileMeta> getSliceQuery(Datastore db, String ea,
      MongoObjectIdTimeSlice slice) {
    // 创建时间分片查询，优先使用从库读取
    return db.createQuery(NFileMeta.class)
        .useReadPreference(ReadPreference.secondaryPreferred())
        .field(NFileMetaFieIds.id).greaterThan(slice.start())
        .field(NFileMetaFieIds.id).lessThan(slice.end())
        .field(NFileMetaFieIds.ea).equal(ea)
        .retrievedFields(true,NFileMetaFieIds.size);
  }

  private AggregationPipeline getAggPipeline(Datastore db,Query<NFileMeta> sliceQuery) {
    // 创建聚合管道
    return db.createAggregation(NFileMeta.class)
        .match(sliceQuery)
        .project(Projection.projection(NFileMetaFieIds.size));
  }

  @Override
  public Optional<EnterpriseFileSizeCount> statisticalFileSizeCount(Datastore db, String ea,
      MongoObjectIdTimeSlice slice) {

    Query<NFileMeta> sliceQuery = getSliceQuery(db, ea, slice);

    AggregationPipeline aggPipeline = getAggPipeline(db, sliceQuery);
    // 统计文件大小与数量并映射为 EnterpriseFileSizeCount
    aggPipeline.group(
        (String) null, // 明确转换为String类型，表示不分组，统计所有文档
        Group.grouping(EnterpriseFileSizeCount.Fields.TOTAL_SIZE,
            Group.sum(NFileMetaFieIds.size)), // 使用$sum累计器统计总文件大小
        Group.grouping(EnterpriseFileSizeCount.Fields.COUNT,
            new Accumulator("$sum", 1)) // 使用$sum累计器统计文件数量，每个文档计为1
    );

    // 执行聚合查询并将结果映射为EnterpriseFileSizeCount对象
    Iterator<EnterpriseFileSizeCount> iterator = aggPipeline.aggregate(EnterpriseFileSizeCount.class);

    if (iterator.hasNext()) {
      return Optional.of(iterator.next());
    }

    // 如果没有结果，返回空Optional
    return Optional.empty();
  }

}
