package com.fxiaoke.com.file.datax.stats.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.com.file.datax.stats.config.CmsPropertiesConfig;
import com.fxiaoke.com.file.datax.stats.domain.model.EnterpriseBaseInfo;
import com.fxiaoke.com.file.datax.stats.service.AsmService;
import com.github.autoconf.helper.ConfigEiHelper;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "AsmServiceImpl")
public class AsmServiceImpl implements AsmService {

  private static final String MODULE = "AsmServiceImpl";

  private final CmsPropertiesConfig config;

  private final EIEAConverter eieaConverter;
  private final EnterpriseEditionService enterpriseEditionService;

  public AsmServiceImpl(CmsPropertiesConfig cmsPropertiesConfig, EIEAConverter eieaConverter,
      EnterpriseEditionService enterpriseEditionService) {
    this.config = cmsPropertiesConfig;

    this.eieaConverter = eieaConverter;
    this.enterpriseEditionService = enterpriseEditionService;

  }

  @Override
  public int getEid(String ea) {
    return eieaConverter.enterpriseAccountToId(ea);
  }

  @Override
  public String getEa(int eid) {
    return eieaConverter.enterpriseIdToAccount(eid);
  }

  @Override
  public Date getCreateTime(String ea) {
    GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
    arg.setEnterpriseAccount(ea);
    EnterpriseData enterpriseData = enterpriseEditionService.getEnterpriseData(arg)
        .getEnterpriseData();
    return enterpriseData.getCreateTime();
  }

  @Override
  public EnterpriseBaseInfo getBaseEnterpriseInfo(String ea) {
    GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
    arg.setEnterpriseAccount(ea);
    EnterpriseData enterpriseData = enterpriseEditionService.getEnterpriseData(arg)
        .getEnterpriseData();
    String tenantId = String.valueOf(enterpriseData.getEnterpriseId());
    String fastDfsAddress = enterpriseData.getDfsAddress();
    Date createTime = enterpriseData.getCreateTime();
    return new EnterpriseBaseInfo(ea, tenantId, createTime, fastDfsAddress);
  }

  // 判断当前用户是否是当前云的用户
  @Override
  public boolean isCurrentCloudUser(String tenantId) {
    return ConfigEiHelper.getInstance().isCurrentCloud(tenantId);
  }
}
