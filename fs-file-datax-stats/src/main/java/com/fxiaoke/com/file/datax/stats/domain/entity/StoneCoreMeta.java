package com.fxiaoke.com.file.datax.stats.domain.entity;

import com.fxiaoke.com.file.datax.stats.domain.fieids.StoneCoreMetaFieIds;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Entity(value = "StoneCore", noClassnameStored = true)
@Indexes(@Index(fields = {
    @Field(StoneCoreMetaFieIds.ea)}, options = @IndexOptions(name = StoneCoreMetaFieIds.ea_index_name, background = true)))
public class StoneCoreMeta {

  @Id
  private ObjectId id;

  @Property(StoneCoreMetaFieIds.ea)
  private String ea;

  @Property(StoneCoreMetaFieIds.oldStorageGroup)
  private String oldStorageGroup;

  @Property(StoneCoreMetaFieIds.newStorageGroup)
  private String newStorageGroup;

  @Property(StoneCoreMetaFieIds.tempStorageGroup)
  private String tempStorageGroup;

  @Property(StoneCoreMetaFieIds.transferStatus)
  private int transferStatus;

  @Property(StoneCoreMetaFieIds.createTime)
  private Date createTime;

  @Property(StoneCoreMetaFieIds.updateTime)
  private Date updateTime;
}
