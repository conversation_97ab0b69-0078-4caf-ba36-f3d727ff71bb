package com.fxiaoke.com.file.datax.stats.config.condition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.context.annotation.Conditional;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Conditional(DataStoreRegisterCondition.class)
public @interface ConditionalOnDataStore {
  boolean firstShareDataStore() default false;
}
