package com.fxiaoke.com.file.datax.stats.domain.model;

import com.fxiaoke.com.file.datax.stats.domain.constants.Constant;
import com.fxiaoke.com.file.datax.stats.domain.constants.DataBaseCluster;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class StoneCoreConfig {

  private String ea;
  private String tenantId;

  private String dbName;

  private DataBaseCluster cluster;

  public void setDataBaseClusterByDfsAddress(String dfsAddress) {
    if (dfsAddress.contains("wh1") || dfsAddress.endsWith("n1") || dfsAddress.endsWith("n1/")) {
      this.cluster = DataBaseCluster.N1;
    } else if (dfsAddress.contains("wh2") || dfsAddress.endsWith("n2") || dfsAddress.endsWith(
        "n2/")) {
      this.cluster = DataBaseCluster.N2;
    } else {
      this.cluster = DataBaseCluster.N; // 默认线下集群
    }
  }

  public void setDbNameByCreateTime(String createTimeStr) {
    dbName = Constant.N_DB_PREFIX + createTimeStr;
  }
}
