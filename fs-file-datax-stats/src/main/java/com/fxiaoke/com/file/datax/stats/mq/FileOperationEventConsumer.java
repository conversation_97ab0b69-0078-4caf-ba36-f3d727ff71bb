package com.fxiaoke.com.file.datax.stats.mq;

import com.fxiaoke.com.file.datax.stats.config.CmsPropertiesConfig;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import jakarta.annotation.PreDestroy;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
  * 统一文件操作事件消费者
  * 监听文件上传和删除事件
 */
@Component
public class FileOperationEventConsumer implements ApplicationListener<ContextRefreshedEvent> {

  private final AutoConfMQPushConsumer consumer;

  public FileOperationEventConsumer(CmsPropertiesConfig cmsPropertiesConfig,
      FileOperationEventListener fileOperationEventListener) {
    consumer = new AutoConfMQPushConsumer(cmsPropertiesConfig.getMqConfigName(),
        cmsPropertiesConfig.getFileStatsMQSection(), fileOperationEventListener);
  }

  @PreDestroy
  public void close() {
    if (consumer != null) {
      consumer.close();
    }
  }

  @Override
  public void onApplicationEvent(@NotNull ContextRefreshedEvent event) {
    if (consumer != null && event.getApplicationContext().getParent() == null) {
      consumer.start();
    }
  }

} 