package com.fxiaoke.com.file.datax.stats.config;

import com.github.filter.CoreFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

  final CmsPropertiesConfig cmsPropertiesConfig;

  public WebConfig(CmsPropertiesConfig cmsPropertiesConfig) {
    this.cmsPropertiesConfig = cmsPropertiesConfig;
  }

  @Bean
  public FilterRegistrationBean<CoreFilter> coreFilter(){
    CoreFilter coreFilter=new CoreFilter();
    FilterRegistrationBean<CoreFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(coreFilter);
    registrationBean.setOrder(0);
    registrationBean.addUrlPatterns("/FileProcess/*");
    return registrationBean;
  }
}
